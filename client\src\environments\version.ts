// IMPORTANT: THIS FILE IS AUTO GENERATED! DO NOT MANUALLY EDIT OR CHECKIN!
/* tslint:disable */
export const VERSION = {
    "dirty": true,
    "raw": "v1.8.1309-2881-g37075ff9-dirty",
    "hash": "g37075ff9",
    "distance": 2881,
    "tag": "v1.8.1309",
    "semver": {
        "options": {
            "loose": false,
            "includePrerelease": false
        },
        "loose": false,
        "raw": "v1.8.1309",
        "major": 1,
        "minor": 8,
        "patch": 1309,
        "prerelease": [],
        "build": [],
        "version": "1.8.1309"
    },
    "suffix": "2881-g37075ff9-dirty",
    "semverString": "1.8.1309+2881.g37075ff9",
    "version": "0.0.0"
};
/* tslint:enable */
