import { BrowserWindow, screen, ipcMain, dialog, crashReporter, systemPreferences, shell, app } from "electron";

import * as cp from "child_process";
import * as path from "node:path";
import * as os from "node:os";
import * as fs from "node:fs";
import { glob } from "glob";

import * as jwt from "jsonwebtoken";

import * as config from "./config";
import { log } from "../share/logger";
import * as ipc from "../../../share/types/ipc.constants";
import { InitAudioIPC } from "@share/audio.main";
import { startCheckRuntimeEnv, stopCheckRuntimeEnv } from "./runtime_check";
import { captureScreenToBlob, captureScreenToFile, capturePageToBlob, capturePageToFile } from "./capture_util";
import { postFormData, postForm, getFile } from "./http_util";
import { MediaType, DeskOption, PackSpec, StartAppOption } from "@core-types/joyshell.types";
import { find_app } from "./find_app";
import { getAddon } from "@share/addon";
import { safeCopyFile } from "@share/safe_copyfile";
import { getDaemonAddr, settings } from "./config";
import { expandPath } from "@share/path_service";
import { isWindowProtected, setWindowProtectionState } from "./window";
import { encodeError } from "./errors";
import { startDesk, stopDesk } from "./desk";
import { ZipWriter } from "@core-types/addon.types";
import * as crypt from "@share/crypt";
import { checkEnvWithScript, evalScript } from "./check_env";
import { config_locker } from "./lock";
import { startLinuxCalculator } from "@share/linux_calculator";
import { getAntiVirusProducts } from "@share/antivirus";
import { getIpAddress, getMACAddress, getMachineID } from "@share";
import { isInstalledTWLanguagePacks } from "@share/language_pack";
import { RegInfo } from "@core-types/config.types";
import * as processlist from "./processlist";
import * as cl from "./connectionlist";
import { InitLoadingIPC } from "./ipc-loading";
import { getLocalSession } from "./agent-api";

const fsPromises = fs.promises;
const writeFileAsync = fsPromises.writeFile;

const osext = getAddon("osext.node");
const inputMgr = new osext.InputSourceManager();

let vDesktopCapturer: any = null;

export function init() {
  InitAudioIPC();
  InitLoadingIPC();

  ipcMain.handle(ipc.IPC_GET_SETTINGS, async () => {
    return {
      argv: process.argv.slice(1),
      settings: config.settings,
      reginfo: config.getRegInfo(),
    };
  });

  ipcMain.handle(ipc.IPC_ADD_CRASH_REPORT_PARAMS, (_event, args: Record<string, string>) => {
    for (const k in args) {
      crashReporter.addExtraParameter(k, args[k]);
    }
  });

  ipcMain.handle(ipc.IPC_SHOW_OPENDIALOG, async (event, arg) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    return await dialog.showOpenDialog(win, arg || {});
  });

  ipcMain.handle(ipc.IPC_SHOW_SAVEDIALOG, async (event, arg) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    return await dialog.showSaveDialog(win, arg || {});
  });

  ipcMain.handle(ipc.IPC_POST_FORMDATA, async (_event, args) => {
    try {
      return await postFormData(args);
    } catch (e) {
      log.error("postFormData: ", e);
      return encodeError(e);
    }
  });

  ipcMain.handle(ipc.IPC_GET_FILE, async (_event, args) => {
    try {
      return await getFile(args);
    } catch (e) {
      log.error("getFile: ", e);
      return encodeError(e);
    }
  });

  ipcMain.handle(ipc.IPC_RUNTIME_CHECK, (_event, enable: boolean, blocked: string | any) => {
    if (enable) {
      startCheckRuntimeEnv(blocked);
    } else {
      stopCheckRuntimeEnv();
    }
    return true;
  });

  ipcMain.handle(ipc.IPC_CAPTURE_BLOB, async function (event, options: unknown) {
    const win = BrowserWindow.fromWebContents(event.sender);
    if ((options as any).page) {
      return capturePageToBlob(win, options);
    }
    return captureScreenToBlob(win, options);
  });

  ipcMain.handle(ipc.IPC_CAPTURE_TO_FILE, async function (event, filename, options: unknown) {
    const win = BrowserWindow.fromWebContents(event.sender);
    try {
      if ((options as any).page) {
        return await capturePageToFile(win, filename, options);
      } else {
        return await captureScreenToFile(win, filename, options);
      }
    } catch (e) {
      log.error("captureScreenToFile: ", e);
      return encodeError(e);
    }
  });

  ipcMain.handle(ipc.IPC_SAVE_SCREEN, async function (event, filename, options: any) {
    const win = BrowserWindow.fromWebContents(event.sender);
    let restoreProtection = false;
    const savePage = !!options.page;
    try {
      if (!savePage && isWindowProtected(win)) {
        restoreProtection = true;
        setWindowProtectionState(win, false);
      }

      const buffer = savePage ? await capturePageToBlob(win, options) : await captureScreenToBlob(win, options);
      if (!buffer) {
        return false;
      }

      const FormData = require("form-data");
      const formdata = new FormData();
      formdata.append("file", buffer, { filename: filename });
      formdata.append("filename", path.basename(filename));
      if ((options as any).plain) {
        formdata.append("plain", "1");
      }
      const addr = getDaemonAddr();
      return await postForm({ url: `${addr}/seat/lfile`, form: formdata });
    } catch (e) {
      log.error("saveScreen: ", e);
      return encodeError(e);
    } finally {
      if (restoreProtection) {
        setWindowProtectionState(win, true);
      }
    }
  });

  ipcMain.handle(ipc.IPC_RECORD_SCREEN, async function (_event, filename: string, options: unknown) {
    const audio = getAddon("audio");
    if (vDesktopCapturer) {
      vDesktopCapturer.stop();
      vDesktopCapturer = null;
    }

    const opts: any = Object.assign(
      {
        fps: 2,
        quality: 40,
      },
      options
    );
    filename = expandPath(filename);
    vDesktopCapturer = audio.record_screen(filename, opts);
    vDesktopCapturer.filename = filename;
    return true;
  });

  ipcMain.handle(ipc.IPC_STOP_RECORD_SCREEN, async function () {
    let filename = "";
    if (vDesktopCapturer) {
      filename = vDesktopCapturer.filename;
      vDesktopCapturer.stop();
      vDesktopCapturer = null;
    }
    return filename;
  });

  ipcMain.handle(ipc.IPC_COMBINE_MP4, async function (_event, outfile: string, inputs) {
    const audio = getAddon("audio");
    return audio.combine(outfile, inputs);
  });

  // Win10下检查设备访问状态: Can be not-determined, granted, denied, restricted or unknown.
  ipcMain.handle(ipc.IPC_GET_MEDIA_ACCESS_STATUS, function (_event, mediaType: MediaType) {
    if (["microphone", "camera", "screen"].includes(mediaType)) {
      return systemPreferences.getMediaAccessStatus(mediaType);
    }
    return "unknown";
  });

  ipcMain.handle(ipc.IPC_ASK_MEDIA_ACCESS, async function (_event, mediaType: "microphone" | "camera") {
    if (process.platform === "darwin") {
      return await systemPreferences.askForMediaAccess(mediaType);
    }
    return false;
  });

  ipcMain.handle(ipc.IPC_OPEN_PRIVACY_PREF, async function (_event, name: string) {
    if (process.platform === "darwin") {
      // open "x-apple.systempreferences:com.apple.preference.security?Privacy_Camera"
      // open "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"
      // open "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture"
      await shell.openExternal(`x-apple.systempreferences:com.apple.preference.security?${name}`);
      return true;
    }
    return false;
  });

  // macOS下检查AXTrusted
  ipcMain.handle(ipc.IPC_CHECK_AX_TRUSTED, async function () {
    if (process.platform === "darwin") {
      return osext.checkAXTrusted();
    }
    return true;
  });

  ipcMain.handle(ipc.IPC_GET_BATTERY_INFO, async function () {
    return osext.getBatteryInfo();
  });

  ipcMain.handle(ipc.IPC_GET_WIFI_INFO, async function () {
    return osext.getWifiInfo();
  });

  // find app path
  ipcMain.handle(ipc.IPC_GET_APP_PATH, async function (_event, name: string) {
    try {
      const app = await find_app(name);
      if (!app) {
        return { name: app.name, path: "" };
      }
      if (app.path.length > 0) {
        return { name: app.name, path: path.join(app.path, app.name) };
      }
      return { name: app.name, path: "" };
    } catch (e) {
      log.error("find_app: ", e);
      return { name: name, path: "" };
    }
  });

  // 检测app是否正在运行中
  ipcMain.handle(ipc.IPC_IS_APP_RUNNING, async function (_event, names: string[], restore?: boolean) {
    const osext = getAddon("osext");

    const res = Array<boolean>(names.length);
    res.fill(false);

    const exe_index = new Map<string, number>(); // name -> index
    for (let i = 0; i < names.length; ++i) {
      // 特殊名称(word, excel, etc.)转成执行文件路径
      const app = await find_app(names[i]);
      if (app) {
        exe_index.set(path.join(app.path, app.name).toLowerCase(), i);
      } else {
        exe_index.set(names[i].toLowerCase(), i);
      }
    }

    if (process.platform === "win32" && osext.listWindows) {
      osext.listWindows((w) => {
        const name = w.image_path.toLowerCase();
        if (exe_index.has(name)) {
          res[exe_index.get(name)] = true;
          if (restore && w.minimized) {
            w.focus();
          }
        }
      });
    } else {
      osext.listProcesses((p) => {
        if (p.full_path) {
          const name = p.full_path.toLowerCase();
          if (exe_index.has(name)) {
            res[exe_index.get(name)] = true;
          }
        }
      });
    }

    return res;
  });

  ipcMain.handle(ipc.IPC_START_DESK, async function (_event, options: DeskOption) {
    return startDesk(options);
  });

  ipcMain.handle(ipc.IPC_CLOSE_DESK, async function () {
    stopDesk();
    return true;
  });

  ipcMain.handle(ipc.IPC_PACK_FILES, async function (_event, spec: PackSpec) {
    const mzip = getAddon("mzip");
    const tempfile = path.join(os.tmpdir(), path.basename(spec.filename));
    const w = await mzip.create(tempfile, spec.password || "");
    try {
      return await packFiles(w, spec, tempfile);
    } catch (err) {
      log.error("PackFiles:", err.message);
      return encodeError({ name: "pack", message: err.message });
    } finally {
      w.close();
    }
  });

  ipcMain.handle(ipc.IPC_GET_REGINFO, async () => {
    return config.getRegInfo();
  });

  ipcMain.handle(ipc.IPC_GET_SERVER_HOST, async () => {
    return config.getServerHost();
  });

  ipcMain.handle(ipc.IPC_GET_ANTIVIRUS_PRODUCTS, async () => {
    return await getAntiVirusProducts();
  });

  ipcMain.handle(ipc.IPC_IS_INSTALLED_LANGUAGE_PACK, async () => {
    return await isInstalledTWLanguagePacks();
  });

  ipcMain.handle(ipc.IPC_GET_SYSTEM_INFO, async () => {
    return await get_system_info();
  });

  ipcMain.handle(ipc.IPC_UPDATE_REGINFO, async (_event, info: RegInfo) => {
    config.updateRegInfo(info);
  });

  ipcMain.handle(ipc.IPC_UPDATE_USER_SETTINGS, function (_event, cfg) {
    if (typeof cfg === "object") {
      log.info("remote config:", cfg);
      config.setUserSetting("remote", cfg);
    }
  });

  ipcMain.handle(ipc.IPC_GET_PREFER_LANGS, async () => {
    return app.getPreferredSystemLanguages();
  });

  ipcMain.handle(ipc.IPC_GET_SYSTEM_LOCALE, async () => {
    return app.getSystemLocale();
  });

  ipcMain.handle(ipc.IPC_CHECK_VM, async function () {
    return checkVM();
  });

  ipcMain.handle(ipc.IPC_CHECK_BLUETOOTH, async function () {
    if (process.platform !== "win32") {
      return 0;
    }

    const isWin7 = osext.getOSVersion().startsWith("6.1");
    if (isWin7) {
      return 0;
    }
    return osext.isBluetoothOn();
  });

  ipcMain.handle(ipc.IPC_GET_SYSTEM_PROXY_INFO, async function () {
    return osext.getProxyConfig();
  });

  ipcMain.handle(ipc.IPC_GET_SYSTEM_BOOT_TIME, async function () {
    try {
      const uptimeSeconds = os.uptime();
      const bootTime = Date.now() - uptimeSeconds * 1000;
      return Math.floor(bootTime / 1000);
    } catch (error) {
      log.error("getSystemBootTime error:", error);
      return 0;
    }
  });

  ipcMain.handle(ipc.IPC_SYSTEM_REBOOT, async (_event, setupApp: boolean) => {
    let command = "";
    if (setupApp) {
      await writeFileAsync(path.join(path.dirname(app.getPath("exe")), "restart.txt"), "restart").catch((error) => {
        log.error("writeFile error: ", error);
      });
    }
    return;
    try {
      if (process.platform === "win32") {
        command = "shutdown /r /t 0 /f";
        await cp.exec(command);
        app.quit();
        return;
      } else if (process.platform === "darwin") {
        command = `osascript -e 'tell app "System Events" to restart'`;
        await cp.exec(command);
        app.quit();
        return;
      } else if (process.platform === "linux") {
        try {
          command = "pkexec systemctl reboot";
          await cp.exec(command);
          app.quit();
          return;
        } catch (pkexecError) {
          log.warn(`pkexec systemctl reboot failed: ${pkexecError.message}. Trying systemctl reboot directly.`);
          command = "systemctl reboot";
          await cp.exec(command);
          app.quit();
          return;
        }
      } else {
        log.error("Unsupported platform for reboot:", process.platform);
        throw new Error("Unsupported platform");
      }
    } catch (error) {
      log.error(`Failed to execute reboot command (${command}):`, error);
      throw error;
    }
  });

  ipcMain.handle(ipc.IPC_SIGN_HUB_TOKEN, async (_event, payload: Record<string, unknown>) => {
    return jwt.sign(
      Object.assign({ exp: Math.floor(Date.now() / 1000) + 120 * 60 }, payload),
      "75b086bfc29cdd5119e64c38879adc115972530ea6abf5819a1ccb3ec4bb1842",
      {
        algorithm: "HS256",
        issuer: "jt",
        noTimestamp: true,
      }
    );
  });

  ipcMain.handle(ipc.IPC_ENCRYPT_TEXT, async (_event, text: string, password?: string) => {
    return encryptText(text, password);
  });

  ipcMain.handle(ipc.IPC_GET_PROCESS_LIST, async () => {
    return processlist.getSnapshot();
  });

  ipcMain.handle(ipc.IPC_GET_INPUTSOURCES, async () => {
    return inputMgr.list();
  });

  ipcMain.handle(ipc.IPC_GET_ACTIVE_INPUTSOURCE, async () => {
    return inputMgr.current();
  });

  ipcMain.handle(ipc.IPC_SET_ACTIVE_INPUTSOURCE, async (_event, name: string) => {
    return inputMgr.select(name);
  });

  ipcMain.handle(ipc.IPC_EVAL_CODE, async (event, code: string, type?: string): Promise<any> => {
    type = type || "sandbox";
    if (type === "sandbox") {
      return evalScript(code);
    }
    const win = BrowserWindow.fromWebContents(event.sender);
    if (win) {
      return win.webContents.executeJavaScript(code, true);
    }
    return null;
  });

  ipcMain.handle(ipc.IPC_CHECK_ENV_CODE, async (_event, list: any): Promise<any> => {
    return checkEnvWithScript(list);
  });

  ipcMain.handle(ipc.IPC_ADD_ALLOW_PROCESS, async (_event, names: string): Promise<any> => {
    config_locker("white " + names);
    return true;
  });

  ipcMain.handle(ipc.IPC_EXPANDPATH, async (_event, p: string, options?: any): Promise<string> => {
    return expandPath(p, options);
  });

  ipcMain.handle(ipc.IPC_STARTCALC, async (_event, opts?: any): Promise<boolean> => {
    if (process.platform === "darwin") {
      const options: cp.SpawnOptions = {};
      try {
        const subprocess = cp.spawn("open", ["-a", "Calculator.app"], options);
        subprocess.unref();
        return true;
      } catch (_ex) {
        return false;
      }
    } else if (process.platform === "linux") {
      return startLinuxCalculator();
    }

    if (process.platform !== "win32") {
      return false;
    }

    // Win32
    if (!opts || typeof opts !== "object") {
      return Promise.reject(new Error("invalid arguments"));
    }

    const calculator = path.resolve(process.resourcesPath, process.arch === "x64" ? "../calcd.exe" : "../calc.exe");
    opts = opts || { preferSystem: false }; // 优先使用自带的计算器
    opts.calculator = opts.calculator || calculator;
    if (osext.startCalc(opts)) {
      return true;
    }
    return false;
  });

  ipcMain.handle(ipc.IPC_KILLPROCESS, async (_event, nameOrPid: string | number): Promise<boolean> => {
    try {
      log.info("kill:", nameOrPid);
      if (typeof nameOrPid === "string") {
        const name = nameOrPid.toLowerCase();
        osext.listProcesses((v: any) => {
          if (v.name.length === name.length && v.name.toLowerCase() === name) {
            v.kill();
          }
        });
        return true;
      }
      if (typeof nameOrPid === "number") {
        osext.kill(nameOrPid);
        return true;
      }
    } catch (_err) {
      return false;
    }
    return false;
  });

  ipcMain.handle(ipc.IPC_STARTAPP, async (_event, opts: StartAppOption): Promise<boolean> => {
    if (process.platform !== "win32") {
      const options: cp.SpawnOptions = {};
      try {
        if (opts.cwd) {
          options.cwd = opts.cwd;
        }
        if (opts.detached) {
          options.detached = true;
        }
        const subprocess = cp.spawn(opts.cmd, options);
        subprocess.unref();
        return true;
      } catch (_ex) {
        return false;
      }
    }

    if (opts.activeExists && opts.name) {
      if (osext.activeProcess(opts.name, !!opts.topmost)) {
        if (osext.isAppVisible(opts.name)) {
          return true;
        }
      }
    }

    const runOpts: { detached?: boolean; cwd?: string; show?: number } = {};
    if (opts.detached) {
      runOpts.detached = true;
    }
    if (opts.cwd) {
      runOpts.cwd = opts.cwd;
    }
    if (opts.show) {
      runOpts.show = opts.show;
    }

    log.info("run:", opts.cmd);
    const pid = await osext.runCmd(opts.cmd, runOpts);
    if (pid && opts.topmost) {
      let atttempts = 0;
      const activeIt = () => {
        if (osext.activeProcess(pid, true) || atttempts > 10) {
          return;
        }
        ++atttempts;
        setTimeout(activeIt, 200);
      };
      setTimeout(activeIt, 200);
    }

    return pid > 0;
  });

  ipcMain.handle(ipc.IPC_ACTIVE_PROCESS, async (_event, name: string, opts?: any): Promise<boolean> => {
    opts = opts || {};
    return osext.activeProcess(name, opts.topmost);
  });

  // 返回本地考试数据
  ipcMain.handle(
    ipc.IPC_GET_LOCAL_SESSION,
    async (_event, session: string, permit: string): Promise<{ response: string; form: string }> => {
      if (app.isOnlineTest) {
        return null;
      }
      return getLocalSession(config.getDaemonAddr(), session, permit);
    }
  );

  // 获取当前网络连接
  ipcMain.handle(ipc.IPC_LIST_CONNECTIONS, async () => {
    return cl.getSnapshot();
  });

  // find in page
  const registerFoundInPage = new Map<number, boolean>();
  ipcMain.handle(
    ipc.IPC_FIND_IN_PAGE,
    async (_event, text: string, options?: { forward?: boolean; findNext?: boolean; matchCase?: boolean }) => {
      const webContents = _event.sender;
      if (webContents) {
        if (!registerFoundInPage.has(webContents.id)) {
          webContents.on("found-in-page", (event, result) => {
            webContents.send(ipc.IPC_ON_FOUND_IN_PAGE, result);
            if (result.finalUpdate) {
              webContents.stopFindInPage("clearSelection");
            }
          });
          registerFoundInPage.set(webContents.id, true);
        }
        return webContents.findInPage(text, options);
      }
      return false;
    }
  );

  ipcMain.handle(
    ipc.IPC_STOP_FIND_IN_PAGE,
    async (_event, action: "clearSelection" | "keepSelection" | "activateSelection") => {
      const webContents = _event.sender;
      if (webContents) {
        webContents.removeAllListeners("found-in-page");
        return webContents.stopFindInPage(action);
      }
      return false;
    }
  );
}

async function packFiles(
  w: ZipWriter,
  spec: PackSpec,
  tempfile: string
): Promise<{ filename: string; num_of_files: number; fullpath?: string; size?: number }> {
  const files = await glob(spec.globs, { cwd: spec.workdir, maxDepth: 3, posix: true });
  if (files.length === 0) {
    return {
      filename: spec.filename,
      num_of_files: 0,
    };
  }

  let nums = 0;
  for (const file of files) {
    let newName = file;
    let src = "";
    if (path.isAbsolute(file)) {
      src = file;
      newName = path.basename(file);
    } else {
      src = path.join(spec.workdir, newName);
    }
    if (await w.addFile(src, newName)) {
      ++nums;
    } else {
      log.warn("PackFiles: add file error:", file);
    }
  }
  w.close();

  const stat = await fs.promises.stat(tempfile);

  let dst = tempfile;
  if (spec.is_backup) {
    dst = await safeCopyFile(tempfile, path.join(settings.BACKUP_PATH, spec.filename));
  }

  return {
    filename: spec.filename,
    num_of_files: nums,
    fullpath: dst,
    size: stat.size,
  };
}

function encryptText(text: string, password: string) {
  const key = password || config.kTokenPassword;
  return crypt.encryptText(text, key);
}

async function checkVM() {
  async function checkForVM() {
    try {
      const util = require("util");
      const exec = util.promisify(require("node:child_process").exec);
      const { stdout } = await exec(
        "ioreg -l | grep -i -E 'manufacturer.*(virtualbox|oracle|vmware|parallels)' || [[ $?==0 ]]",
        { encoding: "utf8", timeout: 5000 }
      );
      return !stdout.match(/^\s*$/);
    } catch (e) {
      return false;
    }
  }

  if (process.platform === "darwin") {
    return await checkForVM();
  } else {
    return osext.checkVM();
  }
}

async function get_partions_info(): Promise<any[]> {
  try {
    return Promise.all(
      osext.getDriveInfo().map((name: string) => {
        return osext
          .getPartitionSpace(name)
          .then((v: any) => {
            v["name"] = name;
            return v;
          })
          .catch((e) => {
            log.error("getPartitionSpace:", name, e.message);
            return null;
          });
      })
    ).then((res) => {
      return res.filter((v) => !!v);
    });
  } catch (e) {
    /* handle error */
    log.error("get_partions_info:", e.message);
    return [];
  }
}

function getOSName(): string {
  switch (process.platform) {
    case "win32":
      return "Windows" + (osext.isWin64() ? " x64" : "");

    case "darwin":
      return "macOS";

    case "linux":
      return "Linux";

    default:
      return process.platform;
  }
}

function getOSVersion(): string {
  return process.platform === "win32" ? osext.getOSVersion() : process.getSystemVersion();
}

function getPCType(): "desktop" | "laptop" {
  if (process.platform === "win32") {
    const addon = getAddon("osext");
    const pcType = addon.getPCType();
    if (pcType == 1) {
      return "laptop";
    }
  }
  return "desktop";
}

async function get_system_info() {
  const disp = screen.getPrimaryDisplay();
  const partitions = await get_partions_info();
  const antiVirus = await getAntiVirusProducts();
  const cpus = os.cpus();
  const memory = process.getSystemMemoryInfo();
  const display = {
    size: disp.size,
    colorDepth: disp.colorDepth,
    scaleFactor: disp.scaleFactor,
  };

  if (osext.getTotalMem) {
    // Returns installed physical memory
    const totalmem = osext.getTotalMem();
    if (totalmem > 0) {
      memory.total = totalmem;
    }
  }
  const info = {
    platform: getOSName(),
    version: getOSVersion(),
    deviceID: config.getRegInfo().machine_id ?? getMachineID(),
    pcType: getPCType(),
    hostname: os.hostname(),
    ipAddresses: getIpAddress(),
    physAddresses: getMACAddress(),
    data_path: config.settings.DATA_PATH,
    memory,
    cpus: { model: cpus ? cpus[0].model : "unknown", cores: cpus.length },
    display,
    inputSources: inputMgr
      .list()
      .map((v) => v.name)
      .filter((n: string) => n !== "Ink Correction"),
    partitions,
    antiVirus: [...new Set(antiVirus.map((v) => v.displayName))],
  };
  return info;
}
