{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as M from \"@core-io/io.message.consts\";\nimport { filter, take } from \"rxjs/operators\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./localStorage.service\";\nimport * as i2 from \"./socket/socket.service\";\nimport * as i3 from \"../modal/custom-alert/custom-alert.service\";\nexport class RestoreCardCheckService {\n  constructor(localStorageService, socketService, alertService) {\n    this.localStorageService = localStorageService;\n    this.socketService = socketService;\n    this.alertService = alertService;\n    this.RESTORE_CARD_CHECK_KEY = \"restore_card_check\";\n    this.mockSettings = {}; // 模拟设置存储\n\n    this.socketService.isLogined.pipe(filter(isLogined => isLogined), take(1)).subscribe(() => {\n      this.initialize(this.socketService.clientIO);\n    });\n  }\n  /**\r\n   * 初始化服务，设置消息监听\r\n   */\n\n\n  initialize(clientIO) {\n    var _this = this;\n\n    this.clientIO = clientIO;\n    this.clientIO.onMsg(M.MSG_RESTORE_CARD_CHECK, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (req) {\n        const {\n          session_id,\n          actions,\n          timeout\n        } = req.params;\n        console.log(\"Client received restore card check command:\", session_id, actions);\n\n        try {\n          // 创建状态回调函数\n          const statusCallback = status => {\n            _this.reportRestoreCardStatus(session_id, status);\n          }; // 执行动作序列\n\n\n          const verificationResult = yield _this.executeActions(session_id, actions, statusCallback); // 如果有验证结果，上报最终结果\n\n          if (verificationResult) {\n            _this.reportRestoreCardResult(session_id, verificationResult);\n          }\n\n          console.log(\"Client completed restore card check actions:\", actions);\n        } catch (error) {\n          console.error(\"Client failed to handle restore card check command:\", error);\n\n          _this.reportRestoreCardStatus(session_id, \"Failed\");\n        }\n\n        return true;\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  /**\r\n   * 检查是否在模拟模式下运行\r\n   */\n\n\n  isMockMode() {\n    return !window.joyshell;\n  }\n  /**\r\n   * 获取模拟设置状态（仅在模拟模式下可用）\r\n   */\n\n\n  getMockSettings() {\n    if (!this.isMockMode()) {\n      console.warn(\"RestoreCardCheck: getMockSettings() is only available in mock mode\");\n      return {};\n    }\n\n    return { ...this.mockSettings\n    };\n  }\n  /**\r\n   * 写入还原卡检查标记到系统设置\r\n   */\n\n\n  writeCheckMark(sessionId, pendingActions) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      const timestamp = Date.now();\n      const checkData = {\n        session_id: sessionId,\n        timestamp: timestamp,\n        created_at: new Date().toISOString(),\n        pending_actions: pendingActions || [] // 保存重启后需要执行的动作\n\n      };\n\n      try {\n        if (!window.joyshell) {\n          // 模拟模式：使用内存存储\n          console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode\");\n          _this2.mockSettings[_this2.RESTORE_CARD_CHECK_KEY] = JSON.stringify(checkData);\n          console.log(\"RestoreCardCheck: Check mark written successfully (mock mode)\", checkData);\n          return;\n        } // 真实模式：使用JoyShell\n\n\n        const currentSettings = window.joyshell.GetSettings();\n        const updatedSettings = { ...currentSettings,\n          [_this2.RESTORE_CARD_CHECK_KEY]: JSON.stringify(checkData)\n        };\n        yield window.joyshell.UpdateSettings(updatedSettings);\n        console.log(\"RestoreCardCheck: Check mark written successfully\", checkData);\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to write check mark\", error);\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 保存重启后需要执行的动作\r\n   */\n\n\n  savePendingActions(sessionId, pendingActions) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(`RestoreCardCheck: Saving pending actions for post-reboot execution:`, pendingActions); // 将待执行动作保存到检查标记中\n\n      yield _this3.writeCheckMark(sessionId, pendingActions);\n    })();\n  }\n  /**\r\n   * 验证还原卡检查标记\r\n   */\n\n\n  verifyCheckMark(sessionId) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        let checkDataStr;\n\n        if (!window.joyshell) {\n          // 模拟模式：从内存读取\n          console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode for verification\");\n          checkDataStr = _this4.mockSettings[_this4.RESTORE_CARD_CHECK_KEY];\n        } else {\n          const currentSettings = window.joyshell.GetSettings();\n          checkDataStr = currentSettings[_this4.RESTORE_CARD_CHECK_KEY];\n        }\n\n        if (!checkDataStr) {\n          return {\n            isValid: false,\n            status: \"FILE_NOT_FOUND\"\n          };\n        }\n\n        let checkData;\n\n        try {\n          checkData = JSON.parse(checkDataStr);\n        } catch (parseError) {\n          console.error(\"RestoreCardCheck: Failed to parse check data\", parseError);\n          return {\n            isValid: false,\n            status: \"INVALID_FILE_FORMAT\"\n          };\n        } // 验证session_id\n\n\n        if (checkData.session_id !== sessionId) {\n          console.warn(\"RestoreCardCheck: Session ID mismatch\", {\n            expected: sessionId,\n            found: checkData.session_id\n          });\n          return {\n            isValid: false,\n            status: \"INVALID_FILE_FORMAT\"\n          };\n        } // 检查时间戳是否合理（不能是未来时间，不能太久以前）\n\n\n        const now = Date.now();\n        const checkTime = checkData.timestamp;\n        const timeDiff = now - checkTime; // 如果检查标记是未来时间，或者超过24小时前，认为无效\n\n        if (timeDiff < 0 || timeDiff > 24 * 60 * 60 * 1000) {\n          console.warn(\"RestoreCardCheck: Invalid timestamp\", {\n            checkTime: new Date(checkTime).toISOString(),\n            now: new Date(now).toISOString(),\n            diffMinutes: timeDiff / (1000 * 60)\n          });\n          return {\n            isValid: false,\n            status: \"INVALID_FILE_FORMAT\"\n          };\n        } // 重启检测逻辑\n\n\n        const wasRestarted = yield _this4.detectSystemRestart(checkData);\n        console.log(\"RestoreCardCheck: Check mark verified successfully\", {\n          checkData,\n          wasRestarted,\n          timeDiffMinutes: timeDiff / (1000 * 60)\n        });\n        return {\n          isValid: true,\n          payload: {\n            session_id: checkData.session_id,\n            timestamp: checkData.timestamp\n          },\n          wasRestartedSinceCreation: wasRestarted,\n          status: wasRestarted ? \"SUCCESS\" : \"FAILED\"\n        };\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to verify check mark\", error);\n        return {\n          isValid: false,\n          status: \"READ_ERROR\"\n        };\n      }\n    })();\n  }\n  /**\r\n   * 清理还原卡检查标记\r\n   */\n\n\n  clearCheckMark() {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (!window.joyshell) {\n          // 模拟模式：从内存删除\n          console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode for clearing\");\n          delete _this5.mockSettings[_this5.RESTORE_CARD_CHECK_KEY];\n          console.log(\"RestoreCardCheck: Check mark cleared successfully (mock mode)\");\n          return;\n        }\n\n        const currentSettings = window.joyshell.GetSettings();\n        const {\n          [_this5.RESTORE_CARD_CHECK_KEY]: removed,\n          ...remainingSettings\n        } = currentSettings;\n        yield window.joyshell.UpdateSettings(remainingSettings);\n        console.log(\"RestoreCardCheck: Check mark cleared successfully\");\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to clear check mark\", error);\n      }\n    })();\n  }\n  /**\r\n   * 检测系统是否重启过\r\n   */\n\n\n  detectSystemRestart(checkData) {\n    return _asyncToGenerator(function* () {\n      const now = Date.now();\n      const checkTime = checkData.timestamp;\n      const timeDiff = now - checkTime;\n\n      if (!window.joyshell) {\n        const wasRestarted = true;\n        console.log(`RestoreCardCheck: Mock restart detection - timeDiff: ${timeDiff}ms, wasRestarted: ${wasRestarted}`);\n        return wasRestarted;\n      }\n\n      const bootTime = yield window.joyshell.GetSystemBootTime();\n      console.log(\"RestoreCardCheck: System boot time\", bootTime);\n      return bootTime > checkTime / 1000;\n    })();\n  }\n  /**\r\n   * 执行还原卡检查动作序列\r\n   */\n\n\n  executeActions(sessionId, actions, statusCallback) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(`RestoreCardCheck: Starting action execution for session ${sessionId}`, actions); // 检查是否包含 reboot 动作\n\n      const rebootIndex = actions.indexOf(\"reboot\");\n\n      if (rebootIndex !== -1) {\n        // 如果包含 reboot，分解为两个阶段\n        const preRebootActions = actions.slice(0, rebootIndex + 1); // 包含reboot\n\n        const postRebootActions = actions.slice(rebootIndex + 1);\n        console.log(`RestoreCardCheck: Split actions - Pre-reboot: ${preRebootActions}, Post-reboot: ${postRebootActions}`); // 如果有重启后的动作，保存到检查标记中\n\n        if (postRebootActions.length > 0) {\n          yield _this6.savePendingActions(sessionId, postRebootActions);\n        } // 执行重启前的所有动作（包括reboot），在reboot处停止\n\n\n        return yield _this6.executeActionSequence(sessionId, preRebootActions, statusCallback, true);\n      } else {\n        // 如果不包含 reboot，正常执行所有动作\n        return yield _this6.executeActionSequence(sessionId, actions, statusCallback, false);\n      }\n    })();\n  }\n\n  executeActionSequence(sessionId, actions, statusCallback, stopAtReboot = false) {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      const context = {\n        sessionId,\n        actions,\n        currentActionIndex: 0,\n        statusCallback\n      };\n      let verificationResult = null;\n\n      try {\n        for (let i = 0; i < actions.length; i++) {\n          context.currentActionIndex = i;\n          const action = actions[i];\n          const actionType = stopAtReboot ? \"pre-reboot\" : \"normal\";\n          console.log(`RestoreCardCheck: Executing ${actionType} action ${i + 1}/${actions.length}: ${action}`);\n\n          if (action === \"reboot\" && stopAtReboot) {\n            // 如果是reboot动作且需要在此停止，执行后终止\n            yield _this7.executeSingleAction(action, context);\n            console.log(`RestoreCardCheck: Reboot action executed, program will terminate`);\n            return null;\n          } else {\n            const result = yield _this7.executeSingleAction(action, context); // 如果是verify动作，保存其结果\n\n            if (action === \"verify\" && result) {\n              verificationResult = result;\n            }\n\n            console.log(`RestoreCardCheck: Completed ${actionType} action ${i + 1}/${actions.length}: ${action}`);\n          }\n        }\n\n        const completionType = stopAtReboot ? \"pre-reboot actions\" : \"all actions\";\n        console.log(`RestoreCardCheck: ${completionType} completed successfully for session ${sessionId}`);\n        return verificationResult;\n      } catch (error) {\n        const errorType = stopAtReboot ? \"Pre-reboot\" : \"Normal\";\n        console.error(`RestoreCardCheck: ${errorType} action execution failed for session ${sessionId}`, error);\n\n        if (statusCallback) {\n          statusCallback(\"Failed\");\n        }\n\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 执行单个动作\r\n   */\n\n\n  executeSingleAction(action, context) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      const {\n        sessionId,\n        statusCallback\n      } = context;\n\n      try {\n        switch (action) {\n          case \"write\":\n            yield _this8.executeWriteAction(sessionId, statusCallback);\n            break;\n\n          case \"reboot\":\n            yield _this8.executeRebootAction(sessionId, statusCallback);\n            break;\n\n          case \"verify\":\n            return yield _this8.executeVerifyAction(sessionId, statusCallback);\n\n          default:\n            throw new Error(`Unknown action: ${action}`);\n        }\n      } catch (error) {\n        console.error(`RestoreCardCheck: Failed to execute action ${action}`, error);\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 执行写入动作\r\n   */\n\n\n  executeWriteAction(sessionId, statusCallback) {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(`RestoreCardCheck: Executing write action for session ${sessionId}`);\n\n      if (statusCallback) {\n        statusCallback(\"Writing\");\n      }\n\n      try {\n        yield _this9.writeCheckMark(sessionId);\n        console.log(`RestoreCardCheck: Write action completed for session ${sessionId}`);\n      } catch (error) {\n        console.error(`RestoreCardCheck: Write action failed for session ${sessionId}`, error);\n\n        if (statusCallback) {\n          statusCallback(\"Failed\");\n        }\n\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 执行重启动作\r\n   */\n\n\n  executeRebootAction(sessionId, statusCallback) {\n    var _this10 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(`RestoreCardCheck: Executing reboot action for session ${sessionId}`);\n\n      if (statusCallback) {\n        statusCallback(\"Restarting\");\n      }\n\n      try {\n        _this10.alertService.setValue({\n          status: true,\n          info: {\n            bodyText: \"系统即将重启！\"\n          }\n        });\n\n        yield new Promise(resolve => setTimeout(resolve, 3000));\n        yield _this10.restartSystem();\n        console.log(`RestoreCardCheck: Reboot action initiated for session ${sessionId}`); // 注意：重启后程序会终止，不会执行后续代码\n      } catch (error) {\n        console.error(`RestoreCardCheck: Reboot action failed for session ${sessionId}`, error);\n\n        if (statusCallback) {\n          statusCallback(\"Failed\");\n        }\n\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 执行验证动作\r\n   */\n\n\n  executeVerifyAction(sessionId, statusCallback) {\n    var _this11 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(`RestoreCardCheck: Executing verify action for session ${sessionId}`);\n\n      if (statusCallback) {\n        statusCallback(\"Verifying\");\n      }\n\n      try {\n        const result = yield _this11.verifyCheckMark(sessionId); // if (statusCallback) {\n        //   if (result.isValid) {\n        //     statusCallback(\"Success\");\n        //   } else {\n        //     statusCallback(\"Failed\");\n        //   }\n        // }\n\n        console.log(`RestoreCardCheck: Verify action completed for session ${sessionId}`, result);\n        return result;\n      } catch (error) {\n        console.error(`RestoreCardCheck: Verify action failed for session ${sessionId}`, error);\n\n        if (statusCallback) {\n          statusCallback(\"Failed\");\n        }\n\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 重启系统\r\n   */\n\n\n  restartSystem() {\n    return _asyncToGenerator(function* () {\n      try {\n        console.log(\"RestoreCardCheck: Initiating system restart\");\n\n        if (!window.joyshell) {\n          // 模拟模式：模拟重启成功\n          console.warn(\"RestoreCardCheck: JoyShell not available, simulating system restart\");\n          yield new Promise(resolve => setTimeout(resolve, 10000)); // 模拟重启延迟\n\n          console.log(\"RestoreCardCheck: System restart simulated successfully (mock mode)\");\n          return;\n        }\n\n        console.log(\"RestoreCardCheck: System reboot\");\n        yield window.joyshell.SystemReboot();\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to restart system\", error);\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * 上报还原卡检查状态\r\n   */\n\n\n  reportRestoreCardStatus(sessionId, status) {\n    const regInfo = this.localStorageService.getRegInfo();\n\n    if (regInfo?.seat) {\n      this.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n        session_id: sessionId,\n        seat_number: regInfo.seat,\n        status: status,\n        timestamp: Date.now()\n      });\n    }\n  }\n  /**\r\n   * 上报还原卡检查结果\r\n   */\n\n\n  reportRestoreCardResult(sessionId, result) {\n    const regInfo = this.localStorageService.getRegInfo();\n\n    if (regInfo?.seat) {\n      this.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n        session_id: sessionId,\n        seat_number: regInfo.seat,\n        status: result.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n        timestamp: Date.now(),\n        result: result\n      });\n    }\n  }\n  /**\r\n   * 等待WebSocket连接建立\r\n   */\n\n\n  waitForSocketConnection() {\n    var _this12 = this;\n\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        if (_this12.socketService.isLogined.value) {\n          resolve();\n          return;\n        }\n\n        const subscription = _this12.socketService.isLogined.subscribe(isLogined => {\n          if (isLogined) {\n            subscription.unsubscribe();\n            resolve();\n          }\n        }); // 设置超时，避免无限等待\n\n\n        setTimeout(() => {\n          subscription.unsubscribe();\n          resolve();\n        }, 30000); // 30秒超时\n      });\n    })();\n  }\n  /**\r\n   * 检查并上报还原卡检查结果（启动时调用）\r\n   */\n\n\n  checkAndReportOnStartup() {\n    var _this13 = this;\n\n    return _asyncToGenerator(function* () {\n      console.log(\"RestoreCardCheck: checkAndReportOnStartup\");\n\n      try {\n        // 只在客户端环境下执行\n        if (!window.joyshell || window.is_demo) {\n          return;\n        } // 等待WebSocket连接建立\n\n\n        yield _this13.waitForSocketConnection(); // 获取当前设置，检查是否有还原卡检查标记\n\n        const currentSettings = window.joyshell.GetSettings();\n        const checkDataStr = currentSettings[\"restore_card_check\"];\n\n        if (!checkDataStr) {\n          console.log(\"RestoreCardCheck: No restore card check mark found\");\n          return;\n        }\n\n        let checkData;\n\n        try {\n          checkData = JSON.parse(checkDataStr);\n        } catch (error) {\n          console.error(\"RestoreCardCheck: Failed to parse restore card check data\", error);\n          return;\n        }\n\n        console.log(\"RestoreCardCheck: Found restore card check mark, processing...\", checkData); // 获取当前座位号\n\n        const regInfo = _this13.localStorageService.getRegInfo();\n\n        const seatNumber = regInfo?.seat;\n\n        if (!seatNumber) {\n          console.warn(\"RestoreCardCheck: No seat number available for restore card report\");\n          return;\n        } // 检查是否有重启后需要执行的动作\n\n\n        const pendingActions = checkData.pending_actions || [];\n\n        if (pendingActions.length > 0) {\n          console.log(\"RestoreCardCheck: Found pending actions after reboot:\", pendingActions); // 创建状态回调函数\n\n          const statusCallback = status => {\n            _this13.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n              session_id: checkData.session_id,\n              seat_number: seatNumber,\n              status: status,\n              timestamp: Date.now()\n            });\n          };\n\n          try {\n            // 执行待执行的动作\n            const verificationResult = yield _this13.executeActionSequence(checkData.session_id, pendingActions, statusCallback, false); // 如果有验证结果，上报最终结果\n\n            if (verificationResult) {\n              _this13.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n                session_id: checkData.session_id,\n                seat_number: seatNumber,\n                status: verificationResult.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n                timestamp: Date.now(),\n                result: verificationResult\n              });\n            }\n\n            console.log(\"RestoreCardCheck: Pending actions completed successfully\"); // 清理检查标记\n\n            if (verificationResult && verificationResult.status === \"SUCCESS\") {\n              yield _this13.clearCheckMark();\n              console.log(\"RestoreCardCheck: Restore card check mark cleared after successful completion\");\n            }\n          } catch (error) {\n            console.error(\"RestoreCardCheck: Failed to execute pending actions:\", error);\n\n            _this13.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n              session_id: checkData.session_id,\n              seat_number: seatNumber,\n              status: \"Failed\",\n              timestamp: Date.now()\n            });\n          }\n        } else {\n          // 没有待执行动作，直接验证已有的检查标记\n          console.log(\"RestoreCardCheck: No pending actions, verifying existing check mark...\"); // 先上报验证状态\n\n          _this13.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n            session_id: checkData.session_id,\n            seat_number: seatNumber,\n            status: \"Verifying\",\n            timestamp: Date.now()\n          });\n\n          const result = yield _this13.verifyCheckMark(checkData.session_id);\n\n          _this13.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n            session_id: checkData.session_id,\n            seat_number: seatNumber,\n            status: result.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n            timestamp: Date.now(),\n            result: result\n          });\n\n          console.log(\"RestoreCardCheck: Restore card check result reported\", {\n            session_id: checkData.session_id,\n            seat_number: seatNumber,\n            result: result\n          }); // 清理检查标记（可选，根据需求决定是否保留）\n\n          if (result.status === \"SUCCESS\") {\n            yield _this13.clearCheckMark();\n            console.log(\"RestoreCardCheck: Restore card check mark cleared after successful report\");\n          }\n        }\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to check and report restore card\", error);\n      }\n    })();\n  }\n\n}\n\nRestoreCardCheckService.ɵfac = function RestoreCardCheckService_Factory(t) {\n  return new (t || RestoreCardCheckService)(i0.ɵɵinject(i1.LocalStorageService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.CustomAlertService));\n};\n\nRestoreCardCheckService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: RestoreCardCheckService,\n  factory: RestoreCardCheckService.ɵfac,\n  providedIn: \"root\"\n});", "map": {"version": 3, "mappings": ";AAIA,OAAO,KAAKA,CAAZ,MAAmB,4BAAnB;AAEA,SAASC,MAAT,EAAiBC,IAAjB,QAA6B,gBAA7B;;;;;AAiCA,OAAM,MAAOC,uBAAP,CAA8B;EAKlCC,YACUC,mBADV,EAEUC,aAFV,EAGUC,YAHV,EAG0C;IAFhC;IACA;IACA;IAPO,8BAAyB,oBAAzB;IACT,oBAAuC,EAAvC,CAMkC,CANS;;IAQjD,KAAKD,aAAL,CAAmBE,SAAnB,CACGC,IADH,CAEIR,MAAM,CAAEO,SAAD,IAAeA,SAAhB,CAFV,EAGIN,IAAI,CAAC,CAAD,CAHR,EAKGQ,SALH,CAKa,MAAK;MACd,KAAKC,UAAL,CAAgB,KAAKL,aAAL,CAAmBM,QAAnC;IACD,CAPH;EAQD;EAED;;;;;EAGAD,UAAU,CAACC,QAAD,EAAmB;IAAA;;IAC3B,KAAKA,QAAL,GAAgBA,QAAhB;IACA,KAAKA,QAAL,CAAcC,KAAd,CAAoBb,CAAC,CAACc,sBAAtB;MAAA,6BAA8C,WAAOC,GAAP,EAAc;QAC1D,MAAM;UAAEC,UAAF;UAAcC,OAAd;UAAuBC;QAAvB,IAAmCH,GAAG,CAACI,MAA7C;QACAC,OAAO,CAACC,GAAR,CAAY,6CAAZ,EAA2DL,UAA3D,EAAuEC,OAAvE;;QAEA,IAAI;UACF;UACA,MAAMK,cAAc,GAAIC,MAAD,IAAmB;YACxC,KAAI,CAACC,uBAAL,CAA6BR,UAA7B,EAAyCO,MAAzC;UACD,CAFD,CAFE,CAMF;;;UACA,MAAME,kBAAkB,SAAS,KAAI,CAACC,cAAL,CAAoBV,UAApB,EAAgCC,OAAhC,EAAyCK,cAAzC,CAAjC,CAPE,CASF;;UACA,IAAIG,kBAAJ,EAAwB;YACtB,KAAI,CAACE,uBAAL,CAA6BX,UAA7B,EAAyCS,kBAAzC;UACD;;UAEDL,OAAO,CAACC,GAAR,CAAY,8CAAZ,EAA4DJ,OAA5D;QACD,CAfD,CAeE,OAAOW,KAAP,EAAc;UACdR,OAAO,CAACQ,KAAR,CAAc,qDAAd,EAAqEA,KAArE;;UACA,KAAI,CAACJ,uBAAL,CAA6BR,UAA7B,EAAyC,QAAzC;QACD;;QAED,OAAO,IAAP;MACD,CAzBD;;MAAA;QAAA;MAAA;IAAA;EA0BD;EAED;;;;;EAGOa,UAAU;IACf,OAAO,CAACC,MAAM,CAACC,QAAf;EACD;EAED;;;;;EAGOC,eAAe;IACpB,IAAI,CAAC,KAAKH,UAAL,EAAL,EAAwB;MACtBT,OAAO,CAACa,IAAR,CAAa,oEAAb;MACA,OAAO,EAAP;IACD;;IACD,OAAO,EAAE,GAAG,KAAKC;IAAV,CAAP;EACD;EAED;;;;;EAGMC,cAAc,CAACC,SAAD,EAAoBC,cAApB,EAAwD;IAAA;;IAAA;MAC1E,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;MACA,MAAMC,SAAS,GAAG;QAChBzB,UAAU,EAAEoB,SADI;QAEhBE,SAAS,EAAEA,SAFK;QAGhBI,UAAU,EAAE,IAAIH,IAAJ,GAAWI,WAAX,EAHI;QAIhBC,eAAe,EAAEP,cAAc,IAAI,EAJnB,CAIuB;;MAJvB,CAAlB;;MAOA,IAAI;QACF,IAAI,CAACP,MAAM,CAACC,QAAZ,EAAsB;UACpB;UACAX,OAAO,CAACa,IAAR,CAAa,2DAAb;UACA,MAAI,CAACC,YAAL,CAAkB,MAAI,CAACW,sBAAvB,IAAiDC,IAAI,CAACC,SAAL,CAAeN,SAAf,CAAjD;UACArB,OAAO,CAACC,GAAR,CAAY,+DAAZ,EAA6EoB,SAA7E;UACA;QACD,CAPC,CASF;;;QACA,MAAMO,eAAe,GAAGlB,MAAM,CAACC,QAAP,CAAgBkB,WAAhB,EAAxB;QACA,MAAMC,eAAe,GAAG,EACtB,GAAGF,eADmB;UAEtB,CAAC,MAAI,CAACH,sBAAN,GAA+BC,IAAI,CAACC,SAAL,CAAeN,SAAf;QAFT,CAAxB;QAKA,MAAMX,MAAM,CAACC,QAAP,CAAgBoB,cAAhB,CAA+BD,eAA/B,CAAN;QACA9B,OAAO,CAACC,GAAR,CAAY,mDAAZ,EAAiEoB,SAAjE;MACD,CAlBD,CAkBE,OAAOb,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,8CAAd,EAA8DA,KAA9D;QACA,MAAMA,KAAN;MACD;IA9ByE;EA+B3E;EAED;;;;;EAGcwB,kBAAkB,CAAChB,SAAD,EAAoBC,cAApB,EAAuD;IAAA;;IAAA;MACrFjB,OAAO,CAACC,GAAR,CAAY,qEAAZ,EAAmFgB,cAAnF,EADqF,CAGrF;;MACA,MAAM,MAAI,CAACF,cAAL,CAAoBC,SAApB,EAA+BC,cAA/B,CAAN;IAJqF;EAKtF;EAED;;;;;EAGMgB,eAAe,CAACjB,SAAD,EAAkB;IAAA;;IAAA;MACrC,IAAI;QACF,IAAIkB,YAAJ;;QAEA,IAAI,CAACxB,MAAM,CAACC,QAAZ,EAAsB;UACpB;UACAX,OAAO,CAACa,IAAR,CAAa,4EAAb;UACAqB,YAAY,GAAG,MAAI,CAACpB,YAAL,CAAkB,MAAI,CAACW,sBAAvB,CAAf;QACD,CAJD,MAIO;UACL,MAAMG,eAAe,GAAGlB,MAAM,CAACC,QAAP,CAAgBkB,WAAhB,EAAxB;UACAK,YAAY,GAAGN,eAAe,CAAC,MAAI,CAACH,sBAAN,CAA9B;QACD;;QAED,IAAI,CAACS,YAAL,EAAmB;UACjB,OAAO;YACLC,OAAO,EAAE,KADJ;YAELhC,MAAM,EAAE;UAFH,CAAP;QAID;;QAED,IAAIkB,SAAJ;;QACA,IAAI;UACFA,SAAS,GAAGK,IAAI,CAACU,KAAL,CAAWF,YAAX,CAAZ;QACD,CAFD,CAEE,OAAOG,UAAP,EAAmB;UACnBrC,OAAO,CAACQ,KAAR,CAAc,8CAAd,EAA8D6B,UAA9D;UACA,OAAO;YACLF,OAAO,EAAE,KADJ;YAELhC,MAAM,EAAE;UAFH,CAAP;QAID,CA5BC,CA8BF;;;QACA,IAAIkB,SAAS,CAACzB,UAAV,KAAyBoB,SAA7B,EAAwC;UACtChB,OAAO,CAACa,IAAR,CAAa,uCAAb,EAAsD;YACpDyB,QAAQ,EAAEtB,SAD0C;YAEpDuB,KAAK,EAAElB,SAAS,CAACzB;UAFmC,CAAtD;UAIA,OAAO;YACLuC,OAAO,EAAE,KADJ;YAELhC,MAAM,EAAE;UAFH,CAAP;QAID,CAxCC,CA0CF;;;QACA,MAAMiB,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;QACA,MAAMoB,SAAS,GAAGnB,SAAS,CAACH,SAA5B;QACA,MAAMuB,QAAQ,GAAGrB,GAAG,GAAGoB,SAAvB,CA7CE,CA+CF;;QACA,IAAIC,QAAQ,GAAG,CAAX,IAAgBA,QAAQ,GAAG,KAAK,EAAL,GAAU,EAAV,GAAe,IAA9C,EAAoD;UAClDzC,OAAO,CAACa,IAAR,CAAa,qCAAb,EAAoD;YAClD2B,SAAS,EAAE,IAAIrB,IAAJ,CAASqB,SAAT,EAAoBjB,WAApB,EADuC;YAElDH,GAAG,EAAE,IAAID,IAAJ,CAASC,GAAT,EAAcG,WAAd,EAF6C;YAGlDmB,WAAW,EAAED,QAAQ,IAAI,OAAO,EAAX;UAH6B,CAApD;UAKA,OAAO;YACLN,OAAO,EAAE,KADJ;YAELhC,MAAM,EAAE;UAFH,CAAP;QAID,CA1DC,CA4DF;;;QACA,MAAMwC,YAAY,SAAS,MAAI,CAACC,mBAAL,CAAyBvB,SAAzB,CAA3B;QAEArB,OAAO,CAACC,GAAR,CAAY,oDAAZ,EAAkE;UAChEoB,SADgE;UAEhEsB,YAFgE;UAGhEE,eAAe,EAAEJ,QAAQ,IAAI,OAAO,EAAX;QAHuC,CAAlE;QAMA,OAAO;UACLN,OAAO,EAAE,IADJ;UAELW,OAAO,EAAE;YACPlD,UAAU,EAAEyB,SAAS,CAACzB,UADf;YAEPsB,SAAS,EAAEG,SAAS,CAACH;UAFd,CAFJ;UAML6B,yBAAyB,EAAEJ,YANtB;UAOLxC,MAAM,EAAEwC,YAAY,GAAG,SAAH,GAAe;QAP9B,CAAP;MASD,CA9ED,CA8EE,OAAOnC,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,+CAAd,EAA+DA,KAA/D;QACA,OAAO;UACL2B,OAAO,EAAE,KADJ;UAELhC,MAAM,EAAE;QAFH,CAAP;MAID;IArFoC;EAsFtC;EAED;;;;;EAGM6C,cAAc;IAAA;;IAAA;MAClB,IAAI;QACF,IAAI,CAACtC,MAAM,CAACC,QAAZ,EAAsB;UACpB;UACAX,OAAO,CAACa,IAAR,CAAa,wEAAb;UACA,OAAO,MAAI,CAACC,YAAL,CAAkB,MAAI,CAACW,sBAAvB,CAAP;UACAzB,OAAO,CAACC,GAAR,CAAY,+DAAZ;UACA;QACD;;QAED,MAAM2B,eAAe,GAAGlB,MAAM,CAACC,QAAP,CAAgBkB,WAAhB,EAAxB;QACA,MAAM;UAAE,CAAC,MAAI,CAACJ,sBAAN,GAA+BwB,OAAjC;UAA0C,GAAGC;QAA7C,IAAmEtB,eAAzE;QAEA,MAAMlB,MAAM,CAACC,QAAP,CAAgBoB,cAAhB,CAA+BmB,iBAA/B,CAAN;QACAlD,OAAO,CAACC,GAAR,CAAY,mDAAZ;MACD,CAdD,CAcE,OAAOO,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,8CAAd,EAA8DA,KAA9D;MACD;IAjBiB;EAkBnB;EAED;;;;;EAGcoC,mBAAmB,CAACvB,SAAD,EAAe;IAAA;MAC9C,MAAMD,GAAG,GAAGD,IAAI,CAACC,GAAL,EAAZ;MACA,MAAMoB,SAAS,GAAGnB,SAAS,CAACH,SAA5B;MACA,MAAMuB,QAAQ,GAAGrB,GAAG,GAAGoB,SAAvB;;MAEA,IAAI,CAAC9B,MAAM,CAACC,QAAZ,EAAsB;QACpB,MAAMgC,YAAY,GAAG,IAArB;QACA3C,OAAO,CAACC,GAAR,CAAY,wDAAwDwC,QAAQ,qBAAqBE,YAAY,EAA7G;QACA,OAAOA,YAAP;MACD;;MAED,MAAMQ,QAAQ,SAASzC,MAAM,CAACC,QAAP,CAAgByC,iBAAhB,EAAvB;MACApD,OAAO,CAACC,GAAR,CAAY,oCAAZ,EAAkDkD,QAAlD;MACA,OAAOA,QAAQ,GAAGX,SAAS,GAAG,IAA9B;IAb8C;EAc/C;EAED;;;;;EAGMlC,cAAc,CAClBU,SADkB,EAElBnB,OAFkB,EAGlBK,cAHkB,EAGa;IAAA;;IAAA;MAE/BF,OAAO,CAACC,GAAR,CAAY,2DAA2De,SAAS,EAAhF,EAAoFnB,OAApF,EAF+B,CAI/B;;MACA,MAAMwD,WAAW,GAAGxD,OAAO,CAACyD,OAAR,CAAgB,QAAhB,CAApB;;MAEA,IAAID,WAAW,KAAK,CAAC,CAArB,EAAwB;QACtB;QACA,MAAME,gBAAgB,GAAG1D,OAAO,CAAC2D,KAAR,CAAc,CAAd,EAAiBH,WAAW,GAAG,CAA/B,CAAzB,CAFsB,CAEsC;;QAC5D,MAAMI,iBAAiB,GAAG5D,OAAO,CAAC2D,KAAR,CAAcH,WAAW,GAAG,CAA5B,CAA1B;QAEArD,OAAO,CAACC,GAAR,CACE,iDAAiDsD,gBAAgB,kBAAkBE,iBAAiB,EADtG,EALsB,CAStB;;QACA,IAAIA,iBAAiB,CAACC,MAAlB,GAA2B,CAA/B,EAAkC;UAChC,MAAM,MAAI,CAAC1B,kBAAL,CAAwBhB,SAAxB,EAAmCyC,iBAAnC,CAAN;QACD,CAZqB,CActB;;;QACA,aAAa,MAAI,CAACE,qBAAL,CAA2B3C,SAA3B,EAAsCuC,gBAAtC,EAAwDrD,cAAxD,EAAwE,IAAxE,CAAb;MACD,CAhBD,MAgBO;QACL;QACA,aAAa,MAAI,CAACyD,qBAAL,CAA2B3C,SAA3B,EAAsCnB,OAAtC,EAA+CK,cAA/C,EAA+D,KAA/D,CAAb;MACD;IA1B8B;EA2BhC;;EAEayD,qBAAqB,CACjC3C,SADiC,EAEjCnB,OAFiC,EAGjCK,cAHiC,EAIjC0D,eAAwB,KAJS,EAIJ;IAAA;;IAAA;MAE7B,MAAMC,OAAO,GAA2B;QACtC7C,SADsC;QAEtCnB,OAFsC;QAGtCiE,kBAAkB,EAAE,CAHkB;QAItC5D;MAJsC,CAAxC;MAOA,IAAIG,kBAAkB,GAAkC,IAAxD;;MAEA,IAAI;QACF,KAAK,IAAI0D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlE,OAAO,CAAC6D,MAA5B,EAAoCK,CAAC,EAArC,EAAyC;UACvCF,OAAO,CAACC,kBAAR,GAA6BC,CAA7B;UACA,MAAMC,MAAM,GAAGnE,OAAO,CAACkE,CAAD,CAAtB;UAEA,MAAME,UAAU,GAAGL,YAAY,GAAG,YAAH,GAAkB,QAAjD;UACA5D,OAAO,CAACC,GAAR,CAAY,+BAA+BgE,UAAU,WAAWF,CAAC,GAAG,CAAC,IAAIlE,OAAO,CAAC6D,MAAM,KAAKM,MAAM,EAAlG;;UAEA,IAAIA,MAAM,KAAK,QAAX,IAAuBJ,YAA3B,EAAyC;YACvC;YACA,MAAM,MAAI,CAACM,mBAAL,CAAyBF,MAAzB,EAAiCH,OAAjC,CAAN;YACA7D,OAAO,CAACC,GAAR,CAAY,kEAAZ;YACA,OAAO,IAAP;UACD,CALD,MAKO;YACL,MAAMkE,MAAM,SAAS,MAAI,CAACD,mBAAL,CAAyBF,MAAzB,EAAiCH,OAAjC,CAArB,CADK,CAGL;;YACA,IAAIG,MAAM,KAAK,QAAX,IAAuBG,MAA3B,EAAmC;cACjC9D,kBAAkB,GAAG8D,MAArB;YACD;;YAEDnE,OAAO,CAACC,GAAR,CAAY,+BAA+BgE,UAAU,WAAWF,CAAC,GAAG,CAAC,IAAIlE,OAAO,CAAC6D,MAAM,KAAKM,MAAM,EAAlG;UACD;QACF;;QAED,MAAMI,cAAc,GAAGR,YAAY,GAAG,oBAAH,GAA0B,aAA7D;QACA5D,OAAO,CAACC,GAAR,CAAY,qBAAqBmE,cAAc,uCAAuCpD,SAAS,EAA/F;QACA,OAAOX,kBAAP;MACD,CA5BD,CA4BE,OAAOG,KAAP,EAAc;QACd,MAAM6D,SAAS,GAAGT,YAAY,GAAG,YAAH,GAAkB,QAAhD;QACA5D,OAAO,CAACQ,KAAR,CAAc,qBAAqB6D,SAAS,wCAAwCrD,SAAS,EAA7F,EAAiGR,KAAjG;;QACA,IAAIN,cAAJ,EAAoB;UAClBA,cAAc,CAAC,QAAD,CAAd;QACD;;QACD,MAAMM,KAAN;MACD;IA9C4B;EA+C9B;EAED;;;;;EAGc0D,mBAAmB,CAC/BF,MAD+B,EAE/BH,OAF+B,EAEA;IAAA;;IAAA;MAE/B,MAAM;QAAE7C,SAAF;QAAad;MAAb,IAAgC2D,OAAtC;;MAEA,IAAI;QACF,QAAQG,MAAR;UACE,KAAK,OAAL;YACE,MAAM,MAAI,CAACM,kBAAL,CAAwBtD,SAAxB,EAAmCd,cAAnC,CAAN;YACA;;UACF,KAAK,QAAL;YACE,MAAM,MAAI,CAACqE,mBAAL,CAAyBvD,SAAzB,EAAoCd,cAApC,CAAN;YACA;;UACF,KAAK,QAAL;YACE,aAAa,MAAI,CAACsE,mBAAL,CAAyBxD,SAAzB,EAAoCd,cAApC,CAAb;;UACF;YACE,MAAM,IAAIuE,KAAJ,CAAU,mBAAmBT,MAAM,EAAnC,CAAN;QAVJ;MAYD,CAbD,CAaE,OAAOxD,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,8CAA8CwD,MAAM,EAAlE,EAAsExD,KAAtE;QACA,MAAMA,KAAN;MACD;IApB8B;EAqBhC;EAED;;;;;EAGM8D,kBAAkB,CAACtD,SAAD,EAAoBd,cAApB,EAAmD;IAAA;;IAAA;MACzEF,OAAO,CAACC,GAAR,CAAY,wDAAwDe,SAAS,EAA7E;;MAEA,IAAId,cAAJ,EAAoB;QAClBA,cAAc,CAAC,SAAD,CAAd;MACD;;MAED,IAAI;QACF,MAAM,MAAI,CAACa,cAAL,CAAoBC,SAApB,CAAN;QAEAhB,OAAO,CAACC,GAAR,CAAY,wDAAwDe,SAAS,EAA7E;MACD,CAJD,CAIE,OAAOR,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,qDAAqDQ,SAAS,EAA5E,EAAgFR,KAAhF;;QACA,IAAIN,cAAJ,EAAoB;UAClBA,cAAc,CAAC,QAAD,CAAd;QACD;;QACD,MAAMM,KAAN;MACD;IAjBwE;EAkB1E;EAED;;;;;EAGM+D,mBAAmB,CAACvD,SAAD,EAAoBd,cAApB,EAAmD;IAAA;;IAAA;MAC1EF,OAAO,CAACC,GAAR,CAAY,yDAAyDe,SAAS,EAA9E;;MAEA,IAAId,cAAJ,EAAoB;QAClBA,cAAc,CAAC,YAAD,CAAd;MACD;;MAED,IAAI;QACF,OAAI,CAACf,YAAL,CAAkBuF,QAAlB,CAA2B;UACzBvE,MAAM,EAAE,IADiB;UAEzBwE,IAAI,EAAE;YAAEC,QAAQ,EAAE;UAAZ;QAFmB,CAA3B;;QAIA,MAAM,IAAIC,OAAJ,CAAaC,OAAD,IAAaC,UAAU,CAACD,OAAD,EAAU,IAAV,CAAnC,CAAN;QACA,MAAM,OAAI,CAACE,aAAL,EAAN;QAEAhF,OAAO,CAACC,GAAR,CAAY,yDAAyDe,SAAS,EAA9E,EARE,CASF;MACD,CAVD,CAUE,OAAOR,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,sDAAsDQ,SAAS,EAA7E,EAAiFR,KAAjF;;QACA,IAAIN,cAAJ,EAAoB;UAClBA,cAAc,CAAC,QAAD,CAAd;QACD;;QACD,MAAMM,KAAN;MACD;IAvByE;EAwB3E;EAED;;;;;EAGMgE,mBAAmB,CAACxD,SAAD,EAAoBd,cAApB,EAAmD;IAAA;;IAAA;MAC1EF,OAAO,CAACC,GAAR,CAAY,yDAAyDe,SAAS,EAA9E;;MAEA,IAAId,cAAJ,EAAoB;QAClBA,cAAc,CAAC,WAAD,CAAd;MACD;;MAED,IAAI;QACF,MAAMiE,MAAM,SAAS,OAAI,CAAClC,eAAL,CAAqBjB,SAArB,CAArB,CADE,CAGF;QACA;QACA;QACA;QACA;QACA;QACA;;QAEAhB,OAAO,CAACC,GAAR,CAAY,yDAAyDe,SAAS,EAA9E,EAAkFmD,MAAlF;QACA,OAAOA,MAAP;MACD,CAbD,CAaE,OAAO3D,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,sDAAsDQ,SAAS,EAA7E,EAAiFR,KAAjF;;QACA,IAAIN,cAAJ,EAAoB;UAClBA,cAAc,CAAC,QAAD,CAAd;QACD;;QACD,MAAMM,KAAN;MACD;IA1ByE;EA2B3E;EAED;;;;;EAGMwE,aAAa;IAAA;MACjB,IAAI;QACFhF,OAAO,CAACC,GAAR,CAAY,6CAAZ;;QAEA,IAAI,CAACS,MAAM,CAACC,QAAZ,EAAsB;UACpB;UACAX,OAAO,CAACa,IAAR,CAAa,qEAAb;UACA,MAAM,IAAIgE,OAAJ,CAAaC,OAAD,IAAaC,UAAU,CAACD,OAAD,EAAU,KAAV,CAAnC,CAAN,CAHoB,CAGwC;;UAC5D9E,OAAO,CAACC,GAAR,CAAY,qEAAZ;UACA;QACD;;QAEDD,OAAO,CAACC,GAAR,CAAY,iCAAZ;QACA,MAAMS,MAAM,CAACC,QAAP,CAAgBsE,YAAhB,EAAN;MACD,CAbD,CAaE,OAAOzE,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,4CAAd,EAA4DA,KAA5D;QACA,MAAMA,KAAN;MACD;IAjBgB;EAkBlB;EAED;;;;;EAGQJ,uBAAuB,CAACY,SAAD,EAAoBb,MAApB,EAA+B;IAC5D,MAAM+E,OAAO,GAAG,KAAKjG,mBAAL,CAAyBkG,UAAzB,EAAhB;;IACA,IAAID,OAAO,EAAEE,IAAb,EAAmB;MACjB,KAAK5F,QAAL,CAAc6F,OAAd,CAAsBzG,CAAC,CAAC0G,uBAAxB,EAAiD;QAC/C1F,UAAU,EAAEoB,SADmC;QAE/CuE,WAAW,EAAEL,OAAO,CAACE,IAF0B;QAG/CjF,MAAM,EAAEA,MAHuC;QAI/Ce,SAAS,EAAEC,IAAI,CAACC,GAAL;MAJoC,CAAjD;IAMD;EACF;EAED;;;;;EAGQb,uBAAuB,CAACS,SAAD,EAAoBmD,MAApB,EAA+B;IAC5D,MAAMe,OAAO,GAAG,KAAKjG,mBAAL,CAAyBkG,UAAzB,EAAhB;;IACA,IAAID,OAAO,EAAEE,IAAb,EAAmB;MACjB,KAAK5F,QAAL,CAAc6F,OAAd,CAAsBzG,CAAC,CAAC0G,uBAAxB,EAAiD;QAC/C1F,UAAU,EAAEoB,SADmC;QAE/CuE,WAAW,EAAEL,OAAO,CAACE,IAF0B;QAG/CjF,MAAM,EAAEgE,MAAM,CAAChE,MAAP,KAAkB,SAAlB,GAA8B,SAA9B,GAA0C,QAHH;QAI/Ce,SAAS,EAAEC,IAAI,CAACC,GAAL,EAJoC;QAK/C+C,MAAM,EAAEA;MALuC,CAAjD;IAOD;EACF;EAED;;;;;EAGcqB,uBAAuB;IAAA;;IAAA;MACnC,OAAO,IAAIX,OAAJ,CAAaC,OAAD,IAAY;QAC7B,IAAI,OAAI,CAAC5F,aAAL,CAAmBE,SAAnB,CAA6BqG,KAAjC,EAAwC;UACtCX,OAAO;UACP;QACD;;QAED,MAAMY,YAAY,GAAG,OAAI,CAACxG,aAAL,CAAmBE,SAAnB,CAA6BE,SAA7B,CAAwCF,SAAD,IAAc;UACxE,IAAIA,SAAJ,EAAe;YACbsG,YAAY,CAACC,WAAb;YACAb,OAAO;UACR;QACF,CALoB,CAArB,CAN6B,CAa7B;;;QACAC,UAAU,CAAC,MAAK;UACdW,YAAY,CAACC,WAAb;UACAb,OAAO;QACR,CAHS,EAGP,KAHO,CAAV,CAd6B,CAiBlB;MACZ,CAlBM,CAAP;IADmC;EAoBpC;EAED;;;;;EAGMc,uBAAuB;IAAA;;IAAA;MAC3B5F,OAAO,CAACC,GAAR,CAAY,2CAAZ;;MACA,IAAI;QACF;QACA,IAAI,CAACS,MAAM,CAACC,QAAR,IAAoBD,MAAM,CAACmF,OAA/B,EAAwC;UACtC;QACD,CAJC,CAMF;;;QACA,MAAM,OAAI,CAACL,uBAAL,EAAN,CAPE,CASF;;QACA,MAAM5D,eAAe,GAAGlB,MAAM,CAACC,QAAP,CAAgBkB,WAAhB,EAAxB;QACA,MAAMK,YAAY,GAAIN,eAAuB,CAAC,oBAAD,CAA7C;;QAEA,IAAI,CAACM,YAAL,EAAmB;UACjBlC,OAAO,CAACC,GAAR,CAAY,oDAAZ;UACA;QACD;;QAED,IAAIoB,SAAJ;;QACA,IAAI;UACFA,SAAS,GAAGK,IAAI,CAACU,KAAL,CAAWF,YAAX,CAAZ;QACD,CAFD,CAEE,OAAO1B,KAAP,EAAc;UACdR,OAAO,CAACQ,KAAR,CAAc,2DAAd,EAA2EA,KAA3E;UACA;QACD;;QAEDR,OAAO,CAACC,GAAR,CAAY,gEAAZ,EAA8EoB,SAA9E,EA1BE,CA4BF;;QACA,MAAM6D,OAAO,GAAG,OAAI,CAACjG,mBAAL,CAAyBkG,UAAzB,EAAhB;;QACA,MAAMW,UAAU,GAAGZ,OAAO,EAAEE,IAA5B;;QAEA,IAAI,CAACU,UAAL,EAAiB;UACf9F,OAAO,CAACa,IAAR,CAAa,oEAAb;UACA;QACD,CAnCC,CAqCF;;;QACA,MAAMI,cAAc,GAAGI,SAAS,CAACG,eAAV,IAA6B,EAApD;;QAEA,IAAIP,cAAc,CAACyC,MAAf,GAAwB,CAA5B,EAA+B;UAC7B1D,OAAO,CAACC,GAAR,CAAY,uDAAZ,EAAqEgB,cAArE,EAD6B,CAG7B;;UACA,MAAMf,cAAc,GAAIC,MAAD,IAAmB;YACxC,OAAI,CAACjB,aAAL,CAAmBM,QAAnB,CAA4B6F,OAA5B,CAAoCzG,CAAC,CAAC0G,uBAAtC,EAA+D;cAC7D1F,UAAU,EAAEyB,SAAS,CAACzB,UADuC;cAE7D2F,WAAW,EAAEO,UAFgD;cAG7D3F,MAAM,EAAEA,MAHqD;cAI7De,SAAS,EAAEC,IAAI,CAACC,GAAL;YAJkD,CAA/D;UAMD,CAPD;;UASA,IAAI;YACF;YACA,MAAMf,kBAAkB,SAAS,OAAI,CAACsD,qBAAL,CAC/BtC,SAAS,CAACzB,UADqB,EAE/BqB,cAF+B,EAG/Bf,cAH+B,EAI/B,KAJ+B,CAAjC,CAFE,CASF;;YACA,IAAIG,kBAAJ,EAAwB;cACtB,OAAI,CAACnB,aAAL,CAAmBM,QAAnB,CAA4B6F,OAA5B,CAAoCzG,CAAC,CAAC0G,uBAAtC,EAA+D;gBAC7D1F,UAAU,EAAEyB,SAAS,CAACzB,UADuC;gBAE7D2F,WAAW,EAAEO,UAFgD;gBAG7D3F,MAAM,EAAEE,kBAAkB,CAACF,MAAnB,KAA8B,SAA9B,GAA0C,SAA1C,GAAsD,QAHD;gBAI7De,SAAS,EAAEC,IAAI,CAACC,GAAL,EAJkD;gBAK7D+C,MAAM,EAAE9D;cALqD,CAA/D;YAOD;;YAEDL,OAAO,CAACC,GAAR,CAAY,0DAAZ,EApBE,CAsBF;;YACA,IAAII,kBAAkB,IAAIA,kBAAkB,CAACF,MAAnB,KAA8B,SAAxD,EAAmE;cACjE,MAAM,OAAI,CAAC6C,cAAL,EAAN;cACAhD,OAAO,CAACC,GAAR,CAAY,+EAAZ;YACD;UACF,CA3BD,CA2BE,OAAOO,KAAP,EAAc;YACdR,OAAO,CAACQ,KAAR,CAAc,sDAAd,EAAsEA,KAAtE;;YACA,OAAI,CAACtB,aAAL,CAAmBM,QAAnB,CAA4B6F,OAA5B,CAAoCzG,CAAC,CAAC0G,uBAAtC,EAA+D;cAC7D1F,UAAU,EAAEyB,SAAS,CAACzB,UADuC;cAE7D2F,WAAW,EAAEO,UAFgD;cAG7D3F,MAAM,EAAE,QAHqD;cAI7De,SAAS,EAAEC,IAAI,CAACC,GAAL;YAJkD,CAA/D;UAMD;QACF,CAjDD,MAiDO;UACL;UACApB,OAAO,CAACC,GAAR,CAAY,wEAAZ,EAFK,CAIL;;UACA,OAAI,CAACf,aAAL,CAAmBM,QAAnB,CAA4B6F,OAA5B,CAAoCzG,CAAC,CAAC0G,uBAAtC,EAA+D;YAC7D1F,UAAU,EAAEyB,SAAS,CAACzB,UADuC;YAE7D2F,WAAW,EAAEO,UAFgD;YAG7D3F,MAAM,EAAE,WAHqD;YAI7De,SAAS,EAAEC,IAAI,CAACC,GAAL;UAJkD,CAA/D;;UAOA,MAAM+C,MAAM,SAAS,OAAI,CAAClC,eAAL,CAAqBZ,SAAS,CAACzB,UAA/B,CAArB;;UAEA,OAAI,CAACV,aAAL,CAAmBM,QAAnB,CAA4B6F,OAA5B,CAAoCzG,CAAC,CAAC0G,uBAAtC,EAA+D;YAC7D1F,UAAU,EAAEyB,SAAS,CAACzB,UADuC;YAE7D2F,WAAW,EAAEO,UAFgD;YAG7D3F,MAAM,EAAEgE,MAAM,CAAChE,MAAP,KAAkB,SAAlB,GAA8B,SAA9B,GAA0C,QAHW;YAI7De,SAAS,EAAEC,IAAI,CAACC,GAAL,EAJkD;YAK7D+C,MAAM,EAAEA;UALqD,CAA/D;;UAQAnE,OAAO,CAACC,GAAR,CAAY,sDAAZ,EAAoE;YAClEL,UAAU,EAAEyB,SAAS,CAACzB,UAD4C;YAElE2F,WAAW,EAAEO,UAFqD;YAGlE3B,MAAM,EAAEA;UAH0D,CAApE,EAtBK,CA4BL;;UACA,IAAIA,MAAM,CAAChE,MAAP,KAAkB,SAAtB,EAAiC;YAC/B,MAAM,OAAI,CAAC6C,cAAL,EAAN;YACAhD,OAAO,CAACC,GAAR,CAAY,2EAAZ;UACD;QACF;MACF,CA3HD,CA2HE,OAAOO,KAAP,EAAc;QACdR,OAAO,CAACQ,KAAR,CAAc,2DAAd,EAA2EA,KAA3E;MACD;IA/H0B;EAgI5B;;AAppBiC;;;mBAAvBzB,yBAAuBgH;AAAA;;;SAAvBhH;EAAuBiH,SAAvBjH,uBAAuB;EAAAkH,YAFtB", "names": ["M", "filter", "take", "RestoreCardCheckService", "constructor", "localStorageService", "socketService", "alertService", "isLogined", "pipe", "subscribe", "initialize", "clientIO", "onMsg", "MSG_RESTORE_CARD_CHECK", "req", "session_id", "actions", "timeout", "params", "console", "log", "statusCallback", "status", "reportRestoreCardStatus", "verificationResult", "executeActions", "reportRestoreCardResult", "error", "isMockMode", "window", "joyshell", "getMockSettings", "warn", "mockSettings", "writeCheckMark", "sessionId", "pendingActions", "timestamp", "Date", "now", "checkData", "created_at", "toISOString", "pending_actions", "RESTORE_CARD_CHECK_KEY", "JSON", "stringify", "currentSettings", "GetSettings", "updatedSettings", "UpdateSettings", "savePendingActions", "verifyCheckMark", "checkDataStr", "<PERSON><PERSON><PERSON><PERSON>", "parse", "parseError", "expected", "found", "checkTime", "timeDiff", "diffMinutes", "wasRestarted", "detectSystemRestart", "timeDiffMinutes", "payload", "wasRestartedSinceCreation", "clearCheckMark", "removed", "remainingSettings", "bootTime", "GetSystemBootTime", "rebootIndex", "indexOf", "preRebootActions", "slice", "postRebootActions", "length", "executeActionSequence", "stopAtReboot", "context", "currentActionIndex", "i", "action", "actionType", "executeSingleAction", "result", "completionType", "errorType", "executeWriteAction", "executeRebootAction", "executeVerifyAction", "Error", "setValue", "info", "bodyText", "Promise", "resolve", "setTimeout", "restartSystem", "SystemReboot", "regInfo", "getRegInfo", "seat", "sendMsg", "MSG_RESTORE_CARD_REPORT", "seat_number", "waitForSocketConnection", "value", "subscription", "unsubscribe", "checkAndReportOnStartup", "is_demo", "seatNumber", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\service\\restore-card-check.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\nimport { LocalStorageService } from \"./localStorage.service\";\nimport { IOClient } from \"@core-io/io.client\";\nimport { SocketService } from \"./socket/socket.service\";\nimport * as M from \"@core-io/io.message.consts\";\nimport { CustomAlertService } from \"../modal/custom-alert/custom-alert.service\";\nimport { filter, take } from \"rxjs/operators\";\n\nexport interface RestoreCardCheckResult {\n  isValid: boolean;\n  payload?: { session_id: string; timestamp: number };\n  wasRestartedSinceCreation?: boolean;\n  status:\n    | \"SUCCESS\"\n    | \"FILE_NOT_FOUND\"\n    | \"INVALID_FILE_FORMAT\"\n    | \"HMAC_MISMATCH\"\n    | \"READ_ERROR\"\n    | \"UNKNOWN_ERROR\"\n    | \"FAILED\";\n}\n\nexport type RestoreCardAction = \"write\" | \"reboot\" | \"verify\";\n\nexport interface StatusCallback {\n  (status: string): void;\n}\n\nexport interface ActionExecutionContext {\n  sessionId: string;\n  actions: RestoreCardAction[];\n  currentActionIndex: number;\n  statusCallback?: StatusCallback;\n  timeouts?: { [key: string]: number };\n}\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class RestoreCardCheckService {\n  private readonly RESTORE_CARD_CHECK_KEY = \"restore_card_check\";\n  private mockSettings: { [key: string]: any } = {}; // 模拟设置存储\n  private clientIO: IOClient;\n\n  constructor(\n    private localStorageService: LocalStorageService,\n    private socketService: SocketService,\n    private alertService: CustomAlertService\n  ) {\n    this.socketService.isLogined\n      .pipe(\n        filter((isLogined) => isLogined),\n        take(1)\n      )\n      .subscribe(() => {\n        this.initialize(this.socketService.clientIO);\n      });\n  }\n\n  /**\n   * 初始化服务，设置消息监听\n   */\n  initialize(clientIO: IOClient): void {\n    this.clientIO = clientIO;\n    this.clientIO.onMsg(M.MSG_RESTORE_CARD_CHECK, async (req) => {\n      const { session_id, actions, timeout } = req.params;\n      console.log(\"Client received restore card check command:\", session_id, actions);\n\n      try {\n        // 创建状态回调函数\n        const statusCallback = (status: string) => {\n          this.reportRestoreCardStatus(session_id, status);\n        };\n\n        // 执行动作序列\n        const verificationResult = await this.executeActions(session_id, actions, statusCallback);\n\n        // 如果有验证结果，上报最终结果\n        if (verificationResult) {\n          this.reportRestoreCardResult(session_id, verificationResult);\n        }\n\n        console.log(\"Client completed restore card check actions:\", actions);\n      } catch (error) {\n        console.error(\"Client failed to handle restore card check command:\", error);\n        this.reportRestoreCardStatus(session_id, \"Failed\");\n      }\n\n      return true;\n    });\n  }\n\n  /**\n   * 检查是否在模拟模式下运行\n   */\n  public isMockMode(): boolean {\n    return !window.joyshell;\n  }\n\n  /**\n   * 获取模拟设置状态（仅在模拟模式下可用）\n   */\n  public getMockSettings(): { [key: string]: any } {\n    if (!this.isMockMode()) {\n      console.warn(\"RestoreCardCheck: getMockSettings() is only available in mock mode\");\n      return {};\n    }\n    return { ...this.mockSettings };\n  }\n\n  /**\n   * 写入还原卡检查标记到系统设置\n   */\n  async writeCheckMark(sessionId: string, pendingActions?: RestoreCardAction[]): Promise<void> {\n    const timestamp = Date.now();\n    const checkData = {\n      session_id: sessionId,\n      timestamp: timestamp,\n      created_at: new Date().toISOString(),\n      pending_actions: pendingActions || [], // 保存重启后需要执行的动作\n    };\n\n    try {\n      if (!window.joyshell) {\n        // 模拟模式：使用内存存储\n        console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode\");\n        this.mockSettings[this.RESTORE_CARD_CHECK_KEY] = JSON.stringify(checkData);\n        console.log(\"RestoreCardCheck: Check mark written successfully (mock mode)\", checkData);\n        return;\n      }\n\n      // 真实模式：使用JoyShell\n      const currentSettings = window.joyshell.GetSettings();\n      const updatedSettings = {\n        ...currentSettings,\n        [this.RESTORE_CARD_CHECK_KEY]: JSON.stringify(checkData),\n      };\n\n      await window.joyshell.UpdateSettings(updatedSettings);\n      console.log(\"RestoreCardCheck: Check mark written successfully\", checkData);\n    } catch (error) {\n      console.error(\"RestoreCardCheck: Failed to write check mark\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * 保存重启后需要执行的动作\n   */\n  private async savePendingActions(sessionId: string, pendingActions: RestoreCardAction[]): Promise<void> {\n    console.log(`RestoreCardCheck: Saving pending actions for post-reboot execution:`, pendingActions);\n\n    // 将待执行动作保存到检查标记中\n    await this.writeCheckMark(sessionId, pendingActions);\n  }\n\n  /**\n   * 验证还原卡检查标记\n   */\n  async verifyCheckMark(sessionId: string): Promise<RestoreCardCheckResult> {\n    try {\n      let checkDataStr: string;\n\n      if (!window.joyshell) {\n        // 模拟模式：从内存读取\n        console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode for verification\");\n        checkDataStr = this.mockSettings[this.RESTORE_CARD_CHECK_KEY];\n      } else {\n        const currentSettings = window.joyshell.GetSettings();\n        checkDataStr = currentSettings[this.RESTORE_CARD_CHECK_KEY];\n      }\n\n      if (!checkDataStr) {\n        return {\n          isValid: false,\n          status: \"FILE_NOT_FOUND\",\n        };\n      }\n\n      let checkData;\n      try {\n        checkData = JSON.parse(checkDataStr);\n      } catch (parseError) {\n        console.error(\"RestoreCardCheck: Failed to parse check data\", parseError);\n        return {\n          isValid: false,\n          status: \"INVALID_FILE_FORMAT\",\n        };\n      }\n\n      // 验证session_id\n      if (checkData.session_id !== sessionId) {\n        console.warn(\"RestoreCardCheck: Session ID mismatch\", {\n          expected: sessionId,\n          found: checkData.session_id,\n        });\n        return {\n          isValid: false,\n          status: \"INVALID_FILE_FORMAT\",\n        };\n      }\n\n      // 检查时间戳是否合理（不能是未来时间，不能太久以前）\n      const now = Date.now();\n      const checkTime = checkData.timestamp;\n      const timeDiff = now - checkTime;\n\n      // 如果检查标记是未来时间，或者超过24小时前，认为无效\n      if (timeDiff < 0 || timeDiff > 24 * 60 * 60 * 1000) {\n        console.warn(\"RestoreCardCheck: Invalid timestamp\", {\n          checkTime: new Date(checkTime).toISOString(),\n          now: new Date(now).toISOString(),\n          diffMinutes: timeDiff / (1000 * 60),\n        });\n        return {\n          isValid: false,\n          status: \"INVALID_FILE_FORMAT\",\n        };\n      }\n\n      // 重启检测逻辑\n      const wasRestarted = await this.detectSystemRestart(checkData);\n\n      console.log(\"RestoreCardCheck: Check mark verified successfully\", {\n        checkData,\n        wasRestarted,\n        timeDiffMinutes: timeDiff / (1000 * 60),\n      });\n\n      return {\n        isValid: true,\n        payload: {\n          session_id: checkData.session_id,\n          timestamp: checkData.timestamp,\n        },\n        wasRestartedSinceCreation: wasRestarted,\n        status: wasRestarted ? \"SUCCESS\" : \"FAILED\",\n      };\n    } catch (error) {\n      console.error(\"RestoreCardCheck: Failed to verify check mark\", error);\n      return {\n        isValid: false,\n        status: \"READ_ERROR\",\n      };\n    }\n  }\n\n  /**\n   * 清理还原卡检查标记\n   */\n  async clearCheckMark(): Promise<void> {\n    try {\n      if (!window.joyshell) {\n        // 模拟模式：从内存删除\n        console.warn(\"RestoreCardCheck: JoyShell not available, using mock mode for clearing\");\n        delete this.mockSettings[this.RESTORE_CARD_CHECK_KEY];\n        console.log(\"RestoreCardCheck: Check mark cleared successfully (mock mode)\");\n        return;\n      }\n\n      const currentSettings = window.joyshell.GetSettings();\n      const { [this.RESTORE_CARD_CHECK_KEY]: removed, ...remainingSettings } = currentSettings as any;\n\n      await window.joyshell.UpdateSettings(remainingSettings);\n      console.log(\"RestoreCardCheck: Check mark cleared successfully\");\n    } catch (error) {\n      console.error(\"RestoreCardCheck: Failed to clear check mark\", error);\n    }\n  }\n\n  /**\n   * 检测系统是否重启过\n   */\n  private async detectSystemRestart(checkData: any): Promise<boolean> {\n    const now = Date.now();\n    const checkTime = checkData.timestamp;\n    const timeDiff = now - checkTime;\n\n    if (!window.joyshell) {\n      const wasRestarted = true;\n      console.log(`RestoreCardCheck: Mock restart detection - timeDiff: ${timeDiff}ms, wasRestarted: ${wasRestarted}`);\n      return wasRestarted;\n    }\n\n    const bootTime = await window.joyshell.GetSystemBootTime();\n    console.log(\"RestoreCardCheck: System boot time\", bootTime);\n    return bootTime > checkTime / 1000;\n  }\n\n  /**\n   * 执行还原卡检查动作序列\n   */\n  async executeActions(\n    sessionId: string,\n    actions: RestoreCardAction[],\n    statusCallback?: StatusCallback\n  ): Promise<RestoreCardCheckResult | null> {\n    console.log(`RestoreCardCheck: Starting action execution for session ${sessionId}`, actions);\n\n    // 检查是否包含 reboot 动作\n    const rebootIndex = actions.indexOf(\"reboot\");\n\n    if (rebootIndex !== -1) {\n      // 如果包含 reboot，分解为两个阶段\n      const preRebootActions = actions.slice(0, rebootIndex + 1); // 包含reboot\n      const postRebootActions = actions.slice(rebootIndex + 1);\n\n      console.log(\n        `RestoreCardCheck: Split actions - Pre-reboot: ${preRebootActions}, Post-reboot: ${postRebootActions}`\n      );\n\n      // 如果有重启后的动作，保存到检查标记中\n      if (postRebootActions.length > 0) {\n        await this.savePendingActions(sessionId, postRebootActions);\n      }\n\n      // 执行重启前的所有动作（包括reboot），在reboot处停止\n      return await this.executeActionSequence(sessionId, preRebootActions, statusCallback, true);\n    } else {\n      // 如果不包含 reboot，正常执行所有动作\n      return await this.executeActionSequence(sessionId, actions, statusCallback, false);\n    }\n  }\n\n  private async executeActionSequence(\n    sessionId: string,\n    actions: RestoreCardAction[],\n    statusCallback?: StatusCallback,\n    stopAtReboot: boolean = false\n  ): Promise<RestoreCardCheckResult | null> {\n    const context: ActionExecutionContext = {\n      sessionId,\n      actions,\n      currentActionIndex: 0,\n      statusCallback,\n    };\n\n    let verificationResult: RestoreCardCheckResult | null = null;\n\n    try {\n      for (let i = 0; i < actions.length; i++) {\n        context.currentActionIndex = i;\n        const action = actions[i];\n\n        const actionType = stopAtReboot ? \"pre-reboot\" : \"normal\";\n        console.log(`RestoreCardCheck: Executing ${actionType} action ${i + 1}/${actions.length}: ${action}`);\n\n        if (action === \"reboot\" && stopAtReboot) {\n          // 如果是reboot动作且需要在此停止，执行后终止\n          await this.executeSingleAction(action, context);\n          console.log(`RestoreCardCheck: Reboot action executed, program will terminate`);\n          return null;\n        } else {\n          const result = await this.executeSingleAction(action, context);\n\n          // 如果是verify动作，保存其结果\n          if (action === \"verify\" && result) {\n            verificationResult = result as RestoreCardCheckResult;\n          }\n\n          console.log(`RestoreCardCheck: Completed ${actionType} action ${i + 1}/${actions.length}: ${action}`);\n        }\n      }\n\n      const completionType = stopAtReboot ? \"pre-reboot actions\" : \"all actions\";\n      console.log(`RestoreCardCheck: ${completionType} completed successfully for session ${sessionId}`);\n      return verificationResult;\n    } catch (error) {\n      const errorType = stopAtReboot ? \"Pre-reboot\" : \"Normal\";\n      console.error(`RestoreCardCheck: ${errorType} action execution failed for session ${sessionId}`, error);\n      if (statusCallback) {\n        statusCallback(\"Failed\");\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 执行单个动作\n   */\n  private async executeSingleAction(\n    action: RestoreCardAction,\n    context: ActionExecutionContext\n  ): Promise<RestoreCardCheckResult | void> {\n    const { sessionId, statusCallback } = context;\n\n    try {\n      switch (action) {\n        case \"write\":\n          await this.executeWriteAction(sessionId, statusCallback);\n          break;\n        case \"reboot\":\n          await this.executeRebootAction(sessionId, statusCallback);\n          break;\n        case \"verify\":\n          return await this.executeVerifyAction(sessionId, statusCallback);\n        default:\n          throw new Error(`Unknown action: ${action}`);\n      }\n    } catch (error) {\n      console.error(`RestoreCardCheck: Failed to execute action ${action}`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 执行写入动作\n   */\n  async executeWriteAction(sessionId: string, statusCallback?: StatusCallback): Promise<void> {\n    console.log(`RestoreCardCheck: Executing write action for session ${sessionId}`);\n\n    if (statusCallback) {\n      statusCallback(\"Writing\");\n    }\n\n    try {\n      await this.writeCheckMark(sessionId);\n\n      console.log(`RestoreCardCheck: Write action completed for session ${sessionId}`);\n    } catch (error) {\n      console.error(`RestoreCardCheck: Write action failed for session ${sessionId}`, error);\n      if (statusCallback) {\n        statusCallback(\"Failed\");\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 执行重启动作\n   */\n  async executeRebootAction(sessionId: string, statusCallback?: StatusCallback): Promise<void> {\n    console.log(`RestoreCardCheck: Executing reboot action for session ${sessionId}`);\n\n    if (statusCallback) {\n      statusCallback(\"Restarting\");\n    }\n\n    try {\n      this.alertService.setValue({\n        status: true,\n        info: { bodyText: \"系统即将重启！\" },\n      });\n      await new Promise((resolve) => setTimeout(resolve, 3000));\n      await this.restartSystem();\n\n      console.log(`RestoreCardCheck: Reboot action initiated for session ${sessionId}`);\n      // 注意：重启后程序会终止，不会执行后续代码\n    } catch (error) {\n      console.error(`RestoreCardCheck: Reboot action failed for session ${sessionId}`, error);\n      if (statusCallback) {\n        statusCallback(\"Failed\");\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 执行验证动作\n   */\n  async executeVerifyAction(sessionId: string, statusCallback?: StatusCallback): Promise<RestoreCardCheckResult> {\n    console.log(`RestoreCardCheck: Executing verify action for session ${sessionId}`);\n\n    if (statusCallback) {\n      statusCallback(\"Verifying\");\n    }\n\n    try {\n      const result = await this.verifyCheckMark(sessionId);\n\n      // if (statusCallback) {\n      //   if (result.isValid) {\n      //     statusCallback(\"Success\");\n      //   } else {\n      //     statusCallback(\"Failed\");\n      //   }\n      // }\n\n      console.log(`RestoreCardCheck: Verify action completed for session ${sessionId}`, result);\n      return result;\n    } catch (error) {\n      console.error(`RestoreCardCheck: Verify action failed for session ${sessionId}`, error);\n      if (statusCallback) {\n        statusCallback(\"Failed\");\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 重启系统\n   */\n  async restartSystem(): Promise<void> {\n    try {\n      console.log(\"RestoreCardCheck: Initiating system restart\");\n\n      if (!window.joyshell) {\n        // 模拟模式：模拟重启成功\n        console.warn(\"RestoreCardCheck: JoyShell not available, simulating system restart\");\n        await new Promise((resolve) => setTimeout(resolve, 10000)); // 模拟重启延迟\n        console.log(\"RestoreCardCheck: System restart simulated successfully (mock mode)\");\n        return;\n      }\n\n      console.log(\"RestoreCardCheck: System reboot\");\n      await window.joyshell.SystemReboot();\n    } catch (error) {\n      console.error(\"RestoreCardCheck: Failed to restart system\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * 上报还原卡检查状态\n   */\n  private reportRestoreCardStatus(sessionId: string, status: any): void {\n    const regInfo = this.localStorageService.getRegInfo();\n    if (regInfo?.seat) {\n      this.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n        session_id: sessionId,\n        seat_number: regInfo.seat,\n        status: status,\n        timestamp: Date.now(),\n      });\n    }\n  }\n\n  /**\n   * 上报还原卡检查结果\n   */\n  private reportRestoreCardResult(sessionId: string, result: any): void {\n    const regInfo = this.localStorageService.getRegInfo();\n    if (regInfo?.seat) {\n      this.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n        session_id: sessionId,\n        seat_number: regInfo.seat,\n        status: result.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n        timestamp: Date.now(),\n        result: result,\n      });\n    }\n  }\n\n  /**\n   * 等待WebSocket连接建立\n   */\n  private async waitForSocketConnection(): Promise<void> {\n    return new Promise((resolve) => {\n      if (this.socketService.isLogined.value) {\n        resolve();\n        return;\n      }\n\n      const subscription = this.socketService.isLogined.subscribe((isLogined) => {\n        if (isLogined) {\n          subscription.unsubscribe();\n          resolve();\n        }\n      });\n\n      // 设置超时，避免无限等待\n      setTimeout(() => {\n        subscription.unsubscribe();\n        resolve();\n      }, 30000); // 30秒超时\n    });\n  }\n\n  /**\n   * 检查并上报还原卡检查结果（启动时调用）\n   */\n  async checkAndReportOnStartup(): Promise<void> {\n    console.log(\"RestoreCardCheck: checkAndReportOnStartup\");\n    try {\n      // 只在客户端环境下执行\n      if (!window.joyshell || window.is_demo) {\n        return;\n      }\n\n      // 等待WebSocket连接建立\n      await this.waitForSocketConnection();\n\n      // 获取当前设置，检查是否有还原卡检查标记\n      const currentSettings = window.joyshell.GetSettings();\n      const checkDataStr = (currentSettings as any)[\"restore_card_check\"];\n\n      if (!checkDataStr) {\n        console.log(\"RestoreCardCheck: No restore card check mark found\");\n        return;\n      }\n\n      let checkData;\n      try {\n        checkData = JSON.parse(checkDataStr);\n      } catch (error) {\n        console.error(\"RestoreCardCheck: Failed to parse restore card check data\", error);\n        return;\n      }\n\n      console.log(\"RestoreCardCheck: Found restore card check mark, processing...\", checkData);\n\n      // 获取当前座位号\n      const regInfo = this.localStorageService.getRegInfo();\n      const seatNumber = regInfo?.seat;\n\n      if (!seatNumber) {\n        console.warn(\"RestoreCardCheck: No seat number available for restore card report\");\n        return;\n      }\n\n      // 检查是否有重启后需要执行的动作\n      const pendingActions = checkData.pending_actions || [];\n\n      if (pendingActions.length > 0) {\n        console.log(\"RestoreCardCheck: Found pending actions after reboot:\", pendingActions);\n\n        // 创建状态回调函数\n        const statusCallback = (status: string) => {\n          this.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n            session_id: checkData.session_id,\n            seat_number: seatNumber,\n            status: status as any,\n            timestamp: Date.now(),\n          });\n        };\n\n        try {\n          // 执行待执行的动作\n          const verificationResult = await this.executeActionSequence(\n            checkData.session_id,\n            pendingActions,\n            statusCallback,\n            false\n          );\n\n          // 如果有验证结果，上报最终结果\n          if (verificationResult) {\n            this.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n              session_id: checkData.session_id,\n              seat_number: seatNumber,\n              status: verificationResult.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n              timestamp: Date.now(),\n              result: verificationResult,\n            });\n          }\n\n          console.log(\"RestoreCardCheck: Pending actions completed successfully\");\n\n          // 清理检查标记\n          if (verificationResult && verificationResult.status === \"SUCCESS\") {\n            await this.clearCheckMark();\n            console.log(\"RestoreCardCheck: Restore card check mark cleared after successful completion\");\n          }\n        } catch (error) {\n          console.error(\"RestoreCardCheck: Failed to execute pending actions:\", error);\n          this.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n            session_id: checkData.session_id,\n            seat_number: seatNumber,\n            status: \"Failed\",\n            timestamp: Date.now(),\n          });\n        }\n      } else {\n        // 没有待执行动作，直接验证已有的检查标记\n        console.log(\"RestoreCardCheck: No pending actions, verifying existing check mark...\");\n\n        // 先上报验证状态\n        this.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n          session_id: checkData.session_id,\n          seat_number: seatNumber,\n          status: \"Verifying\",\n          timestamp: Date.now(),\n        });\n\n        const result = await this.verifyCheckMark(checkData.session_id);\n\n        this.socketService.clientIO.sendMsg(M.MSG_RESTORE_CARD_REPORT, {\n          session_id: checkData.session_id,\n          seat_number: seatNumber,\n          status: result.status === \"SUCCESS\" ? \"Success\" : \"Failed\",\n          timestamp: Date.now(),\n          result: result,\n        });\n\n        console.log(\"RestoreCardCheck: Restore card check result reported\", {\n          session_id: checkData.session_id,\n          seat_number: seatNumber,\n          result: result,\n        });\n\n        // 清理检查标记（可选，根据需求决定是否保留）\n        if (result.status === \"SUCCESS\") {\n          await this.clearCheckMark();\n          console.log(\"RestoreCardCheck: Restore card check mark cleared after successful report\");\n        }\n      }\n    } catch (error) {\n      console.error(\"RestoreCardCheck: Failed to check and report restore card\", error);\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}