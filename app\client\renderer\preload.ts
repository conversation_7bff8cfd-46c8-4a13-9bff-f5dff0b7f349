import * as path from "path";
import * as npath from "path";
import * as fs from "fs";
import * as os from "os";
import * as crypto from "crypto";

import { shell, ipcRenderer, contextBridge, webFrame } from "electron";

import * as audio from "@share/audio.renderer";
import { merge } from "@share/merge-object";
import { getIMM } from "./imm";
import * as net from "@share/net";

import { IClientSetting } from "@core-types/settings.types";
import { JoyShell, DiskInfo, CaptureOption, LockOption } from "@core-types/joyshell.client";
import {
  StartAppOption,
  CalcOption,
  MediaType,
  MediaAccessStatus,
  StatInfo,
  PackResult,
  PackSpec,
  DeskOption,
  DownloadOption,
  ProcessInfo,
  ConnectionInfo,
} from "@core-types/joyshell.types";
import * as ipc from "@core-types/ipc.constants";

import { RegInfo } from "@core-types/config.types";

import { deleteFilesRecursively, getAddon, getMachineID } from "@share";
import { writeFile } from "./writefile";
import { ConnEntry, ProcessEntry } from "@core-types/addon.types";

const osext = getAddon("osext");
const dm = new osext.DisplayManager();

process.env.NODE_TLS_REJECT_UNAUTHORIZED = "1";
if (process.env.JOY_TLS_SECURE === "0") {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
}

const fsPromises = fs.promises;
const writeFileAsync = fsPromises.writeFile;

// Disable Electron Security Warnings.
//   see https://electronjs.org/docs/tutorial/security
window["ELECTRON_DISABLE_SECURITY_WARNINGS"] = 1;

process.on("uncaughtException", function (er) {
  console.error("Unhandled Error:", er.message);
  // TODO: DANGERS!!! It's developer's duty to prevent uncaught exceptions
  // setTimeout(() => { process.exit(1); }, 1000);
});

require(`./keyhook`);

const joyshell = {} as JoyShell;
let settings_: IClientSetting;
let reginfo: RegInfo;
let argv_: any;

async function init() {
  console.log("joyshell: initializing...");
  const data = await ipcRenderer.invoke(ipc.IPC_GET_SETTINGS);
  settings_ = data.settings;
  reginfo = data.reginfo;
  argv_ = data.argv;

  // Expose APIs to web page
  if (window.opener) {
    exposeSubJoyShellInMainWorld();
  } else {
    exposeJoyShellInMainWorld();
  }

  console.log("joyshell: initialized");
  return true;
}

const initPromise = init();

joyshell.Initialize = async function (): Promise<boolean> {
  return initPromise;
};

joyshell.WhenReady = function (): Promise<any> {
  return initPromise;
};

joyshell.GetSettings = function (): IClientSetting {
  return settings_;
};

joyshell.UpdateSettings = async function (partial: Record<string, unknown>): Promise<void> {
  if (partial) {
    merge(settings_, partial);
  }

  await ipcRenderer.invoke(ipc.IPC_UPDATE_USER_SETTINGS, partial);
  const data = await ipcRenderer.invoke(ipc.IPC_GET_SETTINGS);
  settings_ = data.settings;

  const event = new CustomEvent("ata-setting-updated", { detail: partial });
  document.dispatchEvent(event);
};

joyshell.GetArgv = function (): string[] {
  return argv_;
};

joyshell.GetRegInfo = function (): RegInfo {
  return reginfo;
};

joyshell.UpdateRegInfo = (info: RegInfo) => {
  reginfo = info;
  return ipcRenderer.invoke(ipc.IPC_UPDATE_REGINFO, info);
};

joyshell.GetServerHost = (): string => {
  return reginfo.server_addr;
};

joyshell.EncryptToken = function (text: string, password?: string) {
  return ipcRenderer.invoke(ipc.IPC_ENCRYPT_TEXT, text, password);
};

joyshell.Reload = function (main?: boolean) {
  ipcRenderer.send("ipc-reload", main);
};

joyshell.Quit = function () {
  ipcRenderer.send("ipc-quit");
};

joyshell.LockDesktop = function (lock: "lock" | "unlock", options?: LockOption): Promise<boolean> {
  return ipcRenderer.invoke("ipc-lock-desktop", lock, options);
};

joyshell.ToggleFullscreen = function (fullscreen: boolean) {
  return ipcRenderer.send("ipc-toggle-fullscreen", fullscreen);
};

joyshell.SetWindowMode = function (mode, options) {
  ipcRenderer.send("ipc-set-window-mode", mode, options);
};

joyshell.CaptureScreen = async function (options?: CaptureOption): Promise<Blob> {
  const buf = await ipcRenderer.invoke(ipc.IPC_CAPTURE_BLOB, options);
  return new Blob([buf.buffer], { type: "image/jpeg" });
};

joyshell.CaptureScreenToFile = async function (filename: string, options?: CaptureOption): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_CAPTURE_TO_FILE, filename, options);
};

joyshell.SaveScreen = async function (
  filename: string,
  options?: CaptureOption
): Promise<{ status: number; error?: string }> {
  return ipcRenderer.invoke(ipc.IPC_SAVE_SCREEN, filename, Object.assign({ quality: 10 }, options || {}));
};

joyshell.RecordScreen = async function (filename: string, options?: any): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_RECORD_SCREEN, filename, options);
};

joyshell.StopRecordScreen = async function (): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_STOP_RECORD_SCREEN);
};

joyshell.CombineMP4 = async function (filename: string, inputs: string[]): Promise<number> {
  return ipcRenderer.invoke(ipc.IPC_COMBINE_MP4, filename, inputs);
};

joyshell.WriteFileAsync = writeFile;
joyshell.StatFile = async function (filename: string, returnMD5?: boolean): Promise<StatInfo> {
  try {
    const st: StatInfo = await fsPromises.stat(filename);
    st.is_file = st.isFile();
    st.is_directory = st.isDirectory();
    if (returnMD5 && st.is_file) {
      st.md5 = await fileMd5(filename);
    }
    return st;
  } catch (e) {
    if (e.code === "ENOENT") {
      return null;
    } else {
      throw e;
    }
  }
};

joyshell.ResolvePath = function (...segments: string[]): string {
  return path.resolve(...segments);
};

joyshell.GetHubToken = function (payload: any, options?: any) {
  return ipcRenderer.invoke(ipc.IPC_SIGN_HUB_TOKEN, payload, options);
};

joyshell.DownloadFile = function (options: DownloadOption) {
  const url = options.url;
  console.log("Download", url, options.cwd ?? "");
  const fullurl = new URL(url, document.baseURI).href;
  options.url = fullurl;
  if (options.unpack) {
    if (!options.cwd) {
      throw new Error("cwd option is required when unpack is true");
    }
  } else {
    if (!options.filename) {
      throw new Error("filename option is required");
    }
  }
  return ipcRenderer.invoke(ipc.IPC_GET_FILE, options);
};

joyshell.DeleteDirectory = async function (path: string): Promise<boolean> {
  console.log("DeleteDirectory: ", path);

  if (path === "." || path === "" || path === "/" || path === settings_.APP_PATH) {
    // Prevents danger deletion
    return false;
  }

  path = npath.resolve(path);
  if (path === settings_.APP_PATH) {
    // Prevents danger deletion
    return false;
  }

  // Delete files in the path with the best efforts
  deleteFilesRecursively(path);

  if (process.platform === "win32") {
    // Deleting files and directories on Windows is tricky and obscure
    await osext.deleteFiles(path).catch(() => false);
  }

  try {
    await fs.promises.rm(path, { recursive: true, force: true, maxRetries: 2 });
    return true;
  } catch (e) {
    console.error("DeleteDirectory:", e.message);
    return false;
  }
};

joyshell.MkDirs = async function (path: string): Promise<boolean> {
  console.log("MkDirs: ", path);

  if (path === "." || path === "") {
    return false;
  }

  try {
    await fs.promises.mkdir(path, { recursive: true });
    return true;
  } catch (e) {
    /* handle error */
    console.error("MkDirs failed: ", e);
    return false;
  }
};

const getFileVersion =
  osext.getFileVersion ||
  function (): null {
    return null;
  };

joyshell.GetFileVersion = function (filename): { FileVersion: string } {
  return getFileVersion(filename);
};

joyshell.ListProcesses = async function (cb?: (e: ProcessEntry) => void): Promise<any> {
  if (cb) {
    return osext.listProcesses(cb);
  }

  const r = [];
  osext.listProcesses((e) => {
    r.push(e);
  });
  return r;
};

joyshell.KillProcess = async function (nameOrPid: string | number): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_KILLPROCESS, nameOrPid);
};

joyshell.GetAntivirusProductions = async function () {
  return ipcRenderer.invoke(ipc.IPC_GET_ANTIVIRUS_PRODUCTS);
};

joyshell.isInstalledTWLanguagePacks = async function () {
  return ipcRenderer.invoke(ipc.IPC_IS_INSTALLED_LANGUAGE_PACK);
};

joyshell.GetFreeSpace = function (dir: string): Promise<DiskInfo> {
  return osext.getPartitionSpace(dir);
};

joyshell.SetCaffeineMode = function (enable: boolean): void {
  return osext.setAppState(enable);
};

joyshell.GetDriveInfo = function (): string[] {
  return osext.getDriveInfo();
};

joyshell.GetTick = function (): number {
  return osext.getTick();
};

if (process.platform === "win32") {
  joyshell.WMIQuery = function (query: string): any {
    return osext.wmiQuery(query);
  };
}

joyshell.CheckEnv = function (list: any): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_CHECK_ENV_CODE, list);
};

joyshell.EvalScript = function (code: string, type?: string): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_EVAL_CODE, code, type);
};

joyshell.AllowProcess = function (names: string): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_ADD_ALLOW_PROCESS, names);
};

joyshell.GetPath = function (name: string): string {
  switch (name) {
    case "exe":
      return path.dirname(process.execPath);
    case "app":
      if (process.platform === "win32") {
        return path.resolve(process.resourcesPath, "..");
      }
      return path.resolve(process.resourcesPath, "..", "..");
    case "resources":
      return process.resourcesPath;
    case "userdesktop":
      if (process.platform === "win32") {
        return osext.getPath(name);
      }
      return path.join(os.homedir(), "Desktop");
    case "home":
      return os.homedir();
    case "temp":
      return os.tmpdir();
    case "data":
      return settings_.DATA_PATH;
    default:
      if (process.platform === "win32") {
        return osext.getPath(name);
      }
  }
  return null;
};

joyshell.ExpandPath = async function (p: string, options?: any): Promise<string> {
  return ipcRenderer.invoke(ipc.IPC_EXPANDPATH, p, options);
};

joyshell.GetMachineID = function (): string {
  return getMachineID();
};

joyshell.GetAddon = function (name: string): any {
  return getAddon(name as any);
};

joyshell.CheckDiskFree = function (): Promise<DiskInfo> {
  const path = settings_.DATA_PATH;
  return joyshell.GetFreeSpace(path);
};

joyshell.GetSystemInfo = async function (): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_GET_SYSTEM_INFO);
};

joyshell.OpenDialog = async function (options?: any): Promise<{ canceled: boolean; filePaths?: string[] }> {
  return ipcRenderer.invoke(ipc.IPC_SHOW_OPENDIALOG, options);
};

joyshell.SaveDialog = async function (options?: any): Promise<{ canceled: boolean; filePaths?: string[] }> {
  return ipcRenderer.invoke(ipc.IPC_SHOW_SAVEDIALOG, options);
};

joyshell.PostForm = async function (options: any): Promise<any> {
  if (options.url && !options.url.startsWith("http")) {
    options.url = new URL(options.url, document.baseURI).href;
  }
  return ipcRenderer.invoke(ipc.IPC_POST_FORMDATA, options);
};

// Check updates
joyshell.CheckUpdate = function () {
  ipcRenderer.send("ipc-update-check");
};

joyshell.ApplyUpdate = function () {
  ipcRenderer.send("ipc-update-apply");
};

joyshell.GetUpdateInfo = async function () {
  return ipcRenderer.invoke(ipc.IPC_UPDATE_INFO_GET);
};

// eslint-disable-next-line @typescript-eslint/ban-types
joyshell.OnUpdateInfo = function (cb: Function) {
  ipcRenderer.on(ipc.IPC_UPDATE_INFO, (_event, type, info) => {
    cb({ type, info });
  });
};

joyshell.OnUpdateFinished = function (cb: Function) {
  ipcRenderer.on(ipc.IPC_UPDATE_FINISHED, (_event, type, info) => {
    cb({ type, info });
  });
};

joyshell.IsReachable = async function (host: string, opt?: { timeout?: number }): Promise<boolean> {
  return net.is_reachable(host, (opt && opt.timeout) || 2000);
};

joyshell.MinimizeWindow = function () {
  ipcRenderer.send("ipc-minimize-window");
};

joyshell.RestoreWindow = function () {
  ipcRenderer.send("ipc-restore-window");
};

joyshell.ToggleProtection = function (enable: boolean) {
  ipcRenderer.send("ipc-toggle-protection", enable);
};

joyshell.BroadcastIPC = function (channel: string, payload: any): void {
  if (channel.startsWith("ipc-")) {
    broadcastFromRenderer(channel, payload);
  } else {
    throw new Error("invalid channel name");
  }
};

joyshell.SendIPC = function (channel: string, payload: any): void {
  if (channel.startsWith("ipc-")) {
    ipcRenderer.send(channel, payload);
  } else {
    throw new Error("invalid channel name");
  }
};

joyshell.SetProxy = async function (proxy: any): Promise<void> {
  return ipcRenderer.invoke(ipc.IPC_SET_PROXY, proxy);
};

joyshell.ResolveProxy = async function (url: string): Promise<string> {
  return ipcRenderer.invoke(ipc.IPC_RESOLVE_PROXY, url);
};

joyshell.GetProcessList = function (): Promise<{ [key: string]: ProcessInfo }> {
  return ipcRenderer.invoke(ipc.IPC_GET_PROCESS_LIST);
};

joyshell.GetConnectionList = function (): Promise<ConnectionInfo[]> {
  return ipcRenderer.invoke(ipc.IPC_LIST_CONNECTIONS);
};

joyshell.SaveFile = async function (filename: string, content: string): Promise<boolean> {
  await writeFileAsync(filename, content);
  return true;
};

joyshell.CheckVM = function () {
  return ipcRenderer.invoke(ipc.IPC_CHECK_VM);
};

joyshell.CheckBluetooth = function () {
  return ipcRenderer.invoke(ipc.IPC_CHECK_BLUETOOTH);
};

joyshell.KillConnections = function (cb: (proto: string, info: ConnEntry) => boolean) {
  return osext.killConnections(cb);
};

function isSafeishURL(url: string): boolean {
  return url.startsWith("http:") || url.startsWith("https:");
}

joyshell.OpenExternal = async function (url: string): Promise<boolean> {
  if (isSafeishURL(url)) {
    return shell.openExternal(url).then(() => true);
  }
  return Promise.resolve(false);
};

joyshell.StartCalculator = (function () {
  let start = 0;
  return async function (opts?: CalcOption): Promise<boolean> {
    if (Date.now() - start < 2000) {
      // 不能频繁调用
      return false;
    }
    start = Date.now();
    return await ipcRenderer.invoke(ipc.IPC_STARTCALC, opts);
  };
})();

joyshell.StartApp = async function (opts?: StartAppOption): Promise<boolean> {
  if (!opts || typeof opts !== "object") {
    return Promise.reject(new Error("invalid arguments"));
  }
  return await ipcRenderer.invoke(ipc.IPC_STARTAPP, opts);
};

joyshell.ActiveProcess = async function (name: string, opts?: { topmost?: boolean }): Promise<boolean> {
  return await ipcRenderer.invoke(ipc.IPC_ACTIVE_PROCESS, name, opts);
};

joyshell.StartRuntimeCheck = async function (names: string | any): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_RUNTIME_CHECK, true, names);
};

joyshell.StopRuntimeCheck = function (): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_RUNTIME_CHECK, false);
};

joyshell.GetNumOfDisplays = function (): number {
  return dm.numOfDisplays();
};

// Win10/MacOS下返回 not-determined, granted, denied, restricted or unknown.
joyshell.GetMediaAccessStatus = async function (mediaType: MediaType): Promise<MediaAccessStatus> {
  return ipcRenderer.invoke(ipc.IPC_GET_MEDIA_ACCESS_STATUS, mediaType);
};

joyshell.AskMediaAccess = async function (mediaType: MediaType): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_ASK_MEDIA_ACCESS, mediaType);
};

joyshell.OpenPrivacyPref = async function (name: string): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_OPEN_PRIVACY_PREF, name);
};

joyshell.CheckAXTrusted = async function (): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_CHECK_AX_TRUSTED);
};

joyshell.GetSystemProxy = async function (): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_GET_SYSTEM_PROXY_INFO);
};

joyshell.GetSystemBootTime = async function (): Promise<number> {
  return ipcRenderer.invoke(ipc.IPC_GET_SYSTEM_BOOT_TIME);
};

joyshell.SystemReboot = async function (setupApp = false): Promise<void> {
  return ipcRenderer.invoke(ipc.IPC_SYSTEM_REBOOT, setupApp);
};

joyshell.GetLocalSession = async function (session, permit: string): Promise<{ response: string; form: string }> {
  return ipcRenderer.invoke(ipc.IPC_GET_LOCAL_SESSION, session, permit);
};

joyshell.SetZoomFactor = function (factor: number) {
  webFrame.setZoomFactor(factor);
};
joyshell.GetZoomFactor = function (): number {
  return webFrame.getZoomFactor();
};

joyshell.SetZoomLevel = function (level: number) {
  webFrame.setZoomLevel(level);
};

joyshell.GetZoomLevel = function (): number {
  return webFrame.getZoomLevel();
};

joyshell.GetPreferedIP = async function (ip?: string) {
  return net.getMyIP(ip);
};

joyshell.GetPreferedMacAddr = async function (ip?: string) {
  ip = ip || "joytest.org";
  if (ip.startsWith("127.0.0.") || ip === "localhost") {
    ip = "joytest.org";
  }
  const ipaddr = await net.getMyIP(ip);
  if (ipaddr) {
    const mac = net.getMacAddress(ipaddr);
    if (mac) {
      return [mac];
    }
  }
  return net.getMACAddress();
};

joyshell.StartWorker = async function (name, url, opts) {
  ipcRenderer.send("request-worker", name, url, opts);
  await waitPort(name, opts?.timeout || 10000);
};

joyshell.StopWorker = async function (name) {
  ipcRenderer.send("stop-worker", name);
};

joyshell.StartDesk = async function (options?: DeskOption): Promise<{ id: string }> {
  return ipcRenderer.invoke(ipc.IPC_START_DESK, options);
};
joyshell.CloseDesk = async function (id: string): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_CLOSE_DESK, id);
};

joyshell.GetAppPath = async function (name: string): Promise<{ name: string; path: string }> {
  return ipcRenderer.invoke(ipc.IPC_GET_APP_PATH, name);
};

joyshell.PackFiles = async function (spec: PackSpec): Promise<PackResult> {
  return ipcRenderer.invoke(ipc.IPC_PACK_FILES, spec);
};

joyshell.IsAppRunning = async function (names: string[], restoreWindow?: boolean): Promise<boolean[]> {
  return ipcRenderer.invoke(ipc.IPC_IS_APP_RUNNING, names, restoreWindow);
};

joyshell.AddAllowHostname = async function (name: string): Promise<void> {
  return ipcRenderer.invoke(ipc.IPC_ADD_ALLOW_HOST, name);
};

joyshell.DelAllowHostname = async function (name: string): Promise<void> {
  return ipcRenderer.invoke(ipc.IPC_DEL_ALLOW_HOST, name);
};

joyshell.GetBatteryInfo = async function (): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_GET_BATTERY_INFO);
};

joyshell.GetWifiInfo = async function (): Promise<any> {
  return ipcRenderer.invoke(ipc.IPC_GET_WIFI_INFO);
};

// find in page
joyshell.FindInPage = async function (
  text: string,
  options?: { forward?: boolean; findNext?: boolean; matchCase?: boolean }
): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_FIND_IN_PAGE, text, options);
};

joyshell.StopFindInPage = async function (
  action: "clearSelection" | "keepSelection" | "activateSelection"
): Promise<boolean> {
  return ipcRenderer.invoke(ipc.IPC_STOP_FIND_IN_PAGE, action);
};

joyshell.OnFoundInPage = function (
  cb: (result: {
    activeMatchOrdinal: number;
    matches: number;
    selectionEnd: number;
    selectionStart: number;
    finalUpdate: boolean;
  }) => void
) {
  ipcRenderer.on(ipc.IPC_ON_FOUND_IN_PAGE, (_, result) => {
    cb(result);
  });
};

Object.defineProperty(joyshell, "AppVersion", {
  get() {
    return settings_.APP_VERSION;
  },
  enumerable: true,
});

Object.defineProperty(joyshell, "PID", {
  get() {
    return process.pid;
  },
  enumerable: true,
});

Object.defineProperty(joyshell, "IME", {
  value: getIMM(),
  enumerable: true,
});

Object.defineProperty(joyshell, "Platform", {
  get() {
    return process.platform;
  },
  enumerable: true,
});

Object.defineProperty(joyshell, "IPAddresses", {
  get() {
    return net.getIpAddress();
  },
  configurable: true,
  enumerable: true,
});

function isVMMac(mac: string): boolean {
  return (
    mac.startsWith("00:15:5d") /*wsl*/ ||
    mac.startsWith("02:42:") /*docker*/ ||
    mac.startsWith("00:50:56") /*vmware*/ ||
    mac.startsWith("08:00:27") /*virtualbox 5.x*/
  );
}

Object.defineProperty(joyshell, "PhysicalAddresses", {
  get() {
    const addrs = net.getMACAddress();
    const realAddrs = addrs.filter((v) => !isVMMac(v));
    if (realAddrs.length > 0) {
      return realAddrs;
    }
    return addrs;
  },
  configurable: true,
  enumerable: true,
});

Object.defineProperty(joyshell, "Audio", {
  get() {
    return audio;
  },
  enumerable: true,
  configurable: false,
});

Object.defineProperty(joyshell, "Arch", {
  get() {
    if (process.arch === "ia32") {
      return "x86";
    }
    return process.arch;
  },
  enumerable: true,
  configurable: false,
});

Object.defineProperty(joyshell, "OSArch", {
  get() {
    if (process.arch === "ia32") {
      return osext.isWin64() ? "x64" : "x86";
    }
    return process.arch;
  },
  enumerable: true,
  configurable: false,
});

joyshell.IsRemoteSession = async (): Promise<boolean> => {
  return process.platform === "win32" ? osext.isRemoteSession() : false;
};

ipcRenderer.on("ipc-post-message", (_, arg) => {
  const event = new CustomEvent("ata-ipc-message", { detail: arg });
  document.dispatchEvent(event);
});

ipcRenderer.on("ipc-broadcast-message", (_, arg) => {
  const event = new CustomEvent("ata-ipc-message", { detail: arg });
  document.dispatchEvent(event);
});

function broadcastFromRenderer(channel: string, payload: any) {
  // send to all other windows
  ipcRenderer.send("ipc-broadcast-send", { channel, payload });
  // in case main process is actually listening on this channel
  // UNCOMMENT this will make broadcast failed!!
  // ipcRenderer.send(channel, { windowId, channel, payload });
}

const eventBindings = {
  // events
  on: (channel: string, callback: (...args: any[]) => void) => {
    if (channel.startsWith("ipc-")) {
      ipcRenderer.on(channel, callback);
    }
  },
  once: (channel: string, callback: (...args: any[]) => void) => {
    if (channel.startsWith("ipc-")) {
      ipcRenderer.once(channel, callback);
    }
  },
  off: function (event: string, listener: (...args: any[]) => void) {
    if (!event.startsWith("ipc-")) {
      return;
    }
    ipcRenderer.off(event, listener);
  },
  addListener: function (event: string, listener: (...args: any[]) => void) {
    if (!event.startsWith("ipc-")) {
      return;
    }
    ipcRenderer.addListener(event, listener);
  },
  removeListener: (channel: string, callback: (...args: any[]) => void) => {
    if (channel.startsWith("ipc-")) {
      ipcRenderer.removeListener(channel, callback);
    }
  },
  removeAllListeners: (channel: string) => {
    if (channel.startsWith("ipc-")) {
      ipcRenderer.removeAllListeners(channel);
    }
  },
  emit: function (channel: string, ...args: any[]): boolean {
    if (channel.startsWith("ipc-")) {
      return ipcRenderer.emit(channel, { sender: null }, ...args);
    } else {
      return false;
    }
  },
};

function exposeJoyShellInMainWorld() {
  Object.assign(joyshell, eventBindings);
  if (process.contextIsolated) {
    contextBridge.exposeInMainWorld("$joyshell", joyshell);
  } else {
    globalThis.joyshell = joyshell;
  }
}

function exposeSubJoyShellInMainWorld() {
  if (process.contextIsolated) {
    contextBridge.exposeInMainWorld(
      "joyshell",
      Object.assign(
        {
          Platform: joyshell.Platform,
          AppVersion: () => {
            return settings_.APP_VERSION;
          },
          SendIPC: joyshell.SendIPC,
          BroadcastIPC: joyshell.BroadcastIPC,
          Quit: joyshell.Quit,
          Reload: joyshell.Reload,
        },
        eventBindings
      )
    );
  } else {
    globalThis.joyshell = Object.assign(
      {
        Platform: joyshell.Platform,
        AppVersion: () => {
          return settings_.APP_VERSION;
        },
        SendIPC: joyshell.SendIPC,
        BroadcastIPC: joyshell.BroadcastIPC,
        Quit: joyshell.Quit,
        Reload: joyshell.Reload,
      },
      eventBindings
    );
  }
}

// fileMd5: md5 for files and their options
function fileMd5(path: string): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    const stream = fs.createReadStream(path);
    const hash = crypto.createHash("md5");
    stream.on("error", reject);
    stream.on("data", (data) => hash.update(data));
    stream.on("end", () => resolve(hash.digest("hex")));
  });
}

// worker
const windowLoaded = new Promise((resolve) => {
  window.onload = resolve;
});

ipcRenderer.on("provide-worker-port", async (event, name) => {
  await windowLoaded;
  window.postMessage({ message: "port", name }, "*", event.ports);
});

async function waitPort(name: string, timeout = 10000) {
  return new Promise<boolean>((resolve, reject) => {
    function cleanup() {
      window.removeEventListener("message", onMessage);
      ipcRenderer.off("request-worker-error", handleError);
    }

    function handleError(_event: any, error: any) {
      cleanup();
      reject(error);
    }

    function onMessage(e: MessageEvent) {
      if (e.source === window && e.data.message === "port" && e.data.name === name) {
        cleanup();
        resolve(true);
      }
    }

    window.addEventListener("message", onMessage);
    ipcRenderer.once("request-worker-error", handleError);

    setTimeout(() => {
      cleanup();
      const err = new Error("timeout") as Error & { name: string; worker: string };
      err.name = "Timeout";
      err.worker = name;
      reject(err);
    }, timeout);
  });
}
